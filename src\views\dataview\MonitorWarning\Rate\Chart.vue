<script setup>
import { computed } from 'vue'
import VChart from 'vue-echarts'
import * as echarts from 'echarts'

// 1. Props 定义保持不变，与你的数据结构一致
const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({ names: [], values: [] }),
  },
})

// 2. 使用 computed 创建响应式的 option，并应用优美的样式
const option = computed(() => {
  return {
    // --- 从第一个优秀示例中复制过来的所有样式 ---
    backgroundColor: '#0A1A3C',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      textStyle: {
        color: '#FFFFFF'
      },
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#333333'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      top: '10%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        // 3. 动态绑定 X 轴数据
        data: props.data.names,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5B6A8A'
          }
        },
        axisTick: {
          show: true,
          alignWithLabel: true,
          lineStyle: {
            color: '#5B6A8A'
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontSize: 14
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5B6A8A'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#FFFFFF',
          fontSize: 14,
          margin: 8
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#3A4B6C',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        // 4. 更新 series 名称以匹配新图表
        name: '处置率',
        type: 'bar',
        barWidth: '30%',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(0, 0, 0, 0.25)'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#63D6F9' },
              { offset: 1, color: '#1E6FFF' }
            ]
          )
        },
        // 5. 动态绑定 Y 轴（柱状图）数据
        data: props.data.values
      }
    ]
  }
})
</script>

<template>
  <!-- 使用 v-chart，它会处理所有底层的 ECharts 实例管理和更新 -->
  <v-chart class="w-full h-full" :option="option" autoresize />
</template>

<style scoped>
/* 可根据需要添加样式 */
</style>