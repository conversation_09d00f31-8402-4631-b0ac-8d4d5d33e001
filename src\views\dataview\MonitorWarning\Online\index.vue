<script setup>
// @ts-nocheck
import Chart from './Chart.vue'
import { getOnlineRate } from '/@/api/dataview/other.ts'
import { ref, watch } from 'vue'

const props = defineProps({
  checklist: {
    type: Array,
    default: () => ['highway'],
  },
})

const chartData = ref({
  names: [],
  values: [],
})

async function fetchData() {
  const routeClassify = {
    all: false,
    highway: props.checklist.includes('highway'),
    national: props.checklist.includes('national'),
    rural: false,
  }
  const params = {
    routeClassify,
    structureType: [1, 2, 3, 4],
  }
  try {
    const res = await getOnlineRate(params)
    const names = res.data.map((item) => item.managementAlias)
    const values = res.data.map((item) => item.onlineRate*100)
    chartData.value = { names, values }
  }
  catch (error) {
    console.error('Failed to fetch online rate:', error)
    chartData.value = { names: [], values: [] }
  }
}

watch(() => props.checklist, () => {
  fetchData()
}, { immediate: true, deep: true })
</script>

<template>
  <div class="w-full h-[200px] p-[1px]">
    <Chart :data="chartData" />
  </div>
</template>

<style scoped>
.el-checkbox {
  margin-right: 16px !important;
}
.el-checkbox__input.is-checked + .el-checkbox__label {
  /* color: #7ecbff !important; */
}
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #7ecbff !important;
  border-color: #7ecbff !important;
}
</style> 