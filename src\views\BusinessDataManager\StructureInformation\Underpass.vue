<template>
  <AvTable
    :option="underpassOption"
    :searchForm="underpassSearchForm"
    :listApi="queryUnderpassChannels"
    :detailApi="querySingleStructureInfo"
    :updateApi="addAndUpdate"
    @search="handleCustomSearch"
    @load="handleCustomLoad"
    @update="handleCustomUpdate"
  />
</template>

<script setup>
import AvTable from '../AvTable.vue'
import { underpassOption, underpassSearchForm } from './configs/underpass-config.js'
import { queryUnderpassChannels, querySingleStructureInfo, addAndUpdate } from '/@/api/dataview/structure'

// 自定义搜索处理（可选）
function handleCustomSearch(params) {
  console.log('下穿通道搜索参数:', params)
  // 可以在这里添加特殊的搜索逻辑
  // 添加下穿通道类型过滤条件
  params.structureType = 4 // 下穿通道的结构类型为4
}

// 自定义加载处理（可选）
function handleCustomLoad() {
  console.log('下穿通道数据加载')
  // 可以在这里添加特殊的加载逻辑
}

// 自定义更新处理（可选）
function handleCustomUpdate(data) {
  console.log('下穿通道数据更新:', data)
  // 确保结构类型为下穿通道
  data.structureType = 4
  // 可以在这里添加特殊的更新逻辑
}
</script>

<style scoped>
/* 下穿通道特有的样式 */
</style>