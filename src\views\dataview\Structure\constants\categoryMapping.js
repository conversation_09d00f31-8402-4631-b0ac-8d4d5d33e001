/**
 * 设备分类映射常量
 * 统一管理设备分类的中英文映射关系
 */
// 中文名称到英文key的映射
export const CATEGORY_KEY_MAP = {
  环境类: 'environment',
  作用类: 'action',
  结构响应类: 'response',
  结构变化类: 'change',
  变形监测: 'L1',
  物理场监测: 'L2',
  影响因素监测: 'L3',
  宏观现象监测: 'L4',
  隧道机电监测:'SDJD'
}

// 英文key到中文名称的映射
export const CATEGORY_NAMES = {
  environment: '环境类',
  response: '结构响应类',
  change: '结构变化类',
  action: '作用类',
  L1: '变形监测',
  L2: '物理场监测',
  L3: '影响因素监测',
  L4: '宏观现象监测',
  video: '监测视频',
  SDJD: '隧道机电监测',
}

// 获取所有分类的英文keys
export const getAllCategoryKeys = () => Object.keys(CATEGORY_NAMES)

// 获取所有分类的中文名称
export const getAllCategoryNames = () => Object.values(CATEGORY_NAMES)

// 根据中文名称获取英文key
export const getCategoryKey = (chineseName) => CATEGORY_KEY_MAP[chineseName]

// 根据英文key获取中文名称
export const getCategoryName = (englishKey) => CATEGORY_NAMES[englishKey]
