<script setup>
import VChart from 'vue-echarts'
import 'echarts'
import { useChart } from '/@/composables/useChart'

const props = defineProps({
  deviceCode: {
    type: String,
    default: '',
  },
  structureType: {
    type: Number,
    default: 0,
  },
  structureId: {
    type: Number,
    default: null,
  },
  dataTab: {
    type: String,
    default: '实时数据',
  },
})

// 使用图表composable
const {
  chartData,
  baseData,
  isLoading,
  hasErrorOrNoData,
  legendSelected,
  option,
  handleLegendSelectChanged,
  setupWatchers
} = useChart(props)

// 设置监听器
setupWatchers()




</script>

<template>
  <div v-loading="isLoading" class="w-full h-full">
    <v-chart v-if="chartData" :option="option" autoresize @legendselectchanged="handleLegendSelectChanged" />
    <div v-else class="flex justify-center items-center h-full">
      <p v-if="hasErrorOrNoData" class="text-gray-400">暂无数据</p>
      <p v-else class="text-gray-400">请先在上方选择一个监测点位</p>
    </div>
  </div>
</template>
