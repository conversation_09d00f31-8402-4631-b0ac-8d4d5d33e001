<script setup>
import Rate from '../Statistics/Rate.vue'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});
</script>

<template>
  <div v-if="props.data.label === '下穿通道'" class="w-full h-full relative p-1 pb-0 pl-3 bg-[#051743]">
    <span class="absolute left-1 top-10 border border-x-0 text-wrap w-4 border-[#2198D1] bg-[#114976] text-[#23A1D2]">{{ props.data.label }}</span>
    <div class="w-full flex flex-col justify-center items-center h-full border border-[#0F348A] text-[14px]">
      <div class="w-full h-1/2 flex justify-center items-center pt-4">
        <div class="w-[72px] h-[72px]  p-1">
          <Rate :rate="props.data.onlineRate" name="在线率" />
        </div>
      </div>
      <div v-for="(i, index) in props.data.monitorType" class="flex-1 flex flex-col justify-center items-center relative">
        <span>{{ i.label }}</span>
        <div class="flex items-center">
          <span class=" text-[#8E1F40]">{{ i.offline }} </span>
          <span class="text-[#24A4D5]">/ {{ i.total }}</span>
        </div>
      </div>

    </div>

  </div>
  <div v-else class="w-full h-full relative p-1 pb-0 pl-3 bg-[#051743]">
    <span class="absolute left-1 top-[calc(50%-20px)] border border-x-0 text-wrap w-4 border-[#2198D1] bg-[#114976] text-[#23A1D2]">{{ props.data.label }}</span>
    <div class="w-full flex right-2 h-full border border-[#0F348A] text-[14px]">
      <div v-for="(i, index) in props.data.monitorType" class="flex-1 flex flex-col justify-center items-center relative" :class="{ 'dashed-border': index < 1 }">
        <span>{{ i.label }}</span>
        <div class="flex items-center">
          <span class=" text-[#8E1F40]">{{ i.offline }} </span>
          <span class="text-[#24A4D5]">/ {{ i.total }}</span>
        </div>
      </div>
      <div class="w-[72px] p-1">
          <Rate :rate="props.data.onlineRate" name="在线率" />
      </div>
    </div>

  </div>
</template>

<style scoped>
.dashed-border::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  border-right: 1px dashed #2563eb;
}
</style>