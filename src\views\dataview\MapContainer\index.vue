<script setup>
import RoadFilter from './components/RoadFilter.vue'
import Category from './components/Category.vue'
import Structures from './components/Structures.vue'
import { dataview } from '/@/stores/dataview'
import { useRoadFilterApi } from '/@/composables/useApi'
import { getStructureData } from '/@/api/dataview/base'
import { useAmap } from './util/useAmap.js'

const { data } = useRoadFilterApi(getStructureData)
const store = dataview()

const { displayStructureList, selectedCategoryItem } = storeToRefs(store)
const { initMap, setMarkers } = useAmap('container')

watch(data, (newData) => {
  if (Array.isArray(newData)) {
    store.structureList = newData;
  } else if (newData && newData.structureUniqueCode) {
    store.structureList = [newData];
  } else {
    store.structureList = [];
  }
}, { deep: true, immediate: true });

watch(selectedCategoryItem, (newItem) => {
  store.removeFilterList('category');

  if (newItem) {
    const { categoryName, itemName } = newItem;
    const path = categoryName === '按监测场景查看' ? ['monitorScenarios'] : ['managementAlias']
    store.setFilterList([{ id: 'category', path, value: itemName }]);
  }
}, { deep: true, immediate: true })

watch(displayStructureList, (newList) => {
  setMarkers(newList || []);
}, { deep: true });

onMounted(() => {
  initMap().then(() => {
    setMarkers(displayStructureList.value || []);
  });
});
</script>

<template>
  <div class="map-container w-full h-full relative">
    <div id="container" class="w-full h-full" />
    <RoadFilter />
    <Category />
    <Structures />
  </div>
</template>

<style scoped>
.map-container {
  position: relative;
}

#container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.road-filter-container {
  background-color: #0a1446;
  /* Dark blue background */
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
  /* Optional: rounded corners */
}

.filter-options {
  display: flex;
  gap: 15px;
  /* Added gap for better spacing */
  margin-right: 15px;
}

.search-section {
  display: flex;
  gap: 10px;
}

.search-btn,
.refresh-btn {
  color: #00D0FF;
  background: transparent;
  border: 1px solid #00D0FF;
}

.search-btn:hover,
.refresh-btn:hover {
  background: #00D0FF1A;
  /* Slight background on hover */
  color: #33daff;
}

/* Styles for the custom themed popover */
:deep(.blue-theme-search-popover) {
  background-color: #0a1446 !important;
  /* Your dark blue background */
  border: 1px solid #00D0FF !important;
  /* Your theme's accent color */
  border-radius: 4px;
  padding: 0;
  /* Remove default el-popover padding if any, control with children */
}

:deep(.blue-theme-search-popover .el-popper__arrow::before) {
  background-color: #0a1446 !important;
  /* Arrow background */
  border-color: #00D0FF !important;
  /* Arrow border */
}

.search-suggestions {
  /* This class is inside your popover template slot */
  padding: 15px;
  /* Add padding here instead of popover root */
  color: white;
}

.search-suggestions h4 {
  color: white;
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

/* Styling for el-input within the themed popover */
:deep(.blue-theme-search-popover .el-input .el-input__wrapper) {
  background-color: #1c2a5e;
  /* A slightly lighter dark blue for input field */
  box-shadow: 0 0 0 1px #00D0FF inset;
  /* Custom border to match theme */
  border-radius: 4px;
}

:deep(.blue-theme-search-popover .el-input .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #33ebff inset;
  /* Brighter border on focus */
}

:deep(.blue-theme-search-popover .el-input .el-input__inner) {
  color: white;
  /* Text color */
}

:deep(.blue-theme-search-popover .el-input .el-input__inner::placeholder) {
  color: #88a1d0;
  /* Placeholder text color */
}

:deep(.blue-theme-search-popover .el-input .el-input__icon) {
  color: #88a1d0;
  /* Clear icon color */
}

:deep(.blue-theme-search-popover .el-input .el-input__clear:hover svg) {
  color: #00D0FF !important;
  /* Clear icon color on hover */
}


.suggestion-list {
  margin-top: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-list::-webkit-scrollbar {
  width: 6px;
}

.suggestion-list::-webkit-scrollbar-thumb {
  background: #00D0FF;
  border-radius: 3px;
}

.suggestion-list::-webkit-scrollbar-track {
  background: #1c2a5e;
}


.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: white;
  /* Text color for suggestions */
  border-radius: 3px;
}

.suggestion-item:hover {
  background-color: #1c2a5e;
  /* Hover background for suggestions */
}

.no-suggestions {
  padding: 10px;
  color: #88a1d0;
  /* Lighter text for "no suggestions" */
  text-align: center;
}

/* Override Element Plus checkbox styles to match the dark theme */
:deep(.el-checkbox__label) {
  color: white;
}

:deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
  color: #00D0FF;
  /* Label color when checked */
}

:deep(.el-checkbox__inner) {
  /* Checkbox box itself */
  background-color: transparent;
  border: 1px solid #00D0FF;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #00D0FF;
  border-color: #00D0FF;
}

:deep(.el-checkbox__input.is-focus .el-checkbox__inner) {
  border-color: #33ebff;
  /* Focus outline for checkbox */
}
</style>