<template>
  <div class="w-full p-2 border border-[#0E3067] bg-[#031538] text-white font-sans relative">
    <header class="border-l-2 border-blue-500 pl-2 mb-3 text-[16px]">
      结构物信息
    </header>
    <div v-loading="isLoading" v-if="structureInfoForDisplay.length > 0" class="grid grid-cols-2 gap-1">
      <el-tooltip
        v-for="item in structureInfoForDisplay"
        :key="item.name"
        placement="top"
        effect="dark"
        :disabled="!item.tooltipCompanyName && (!item.value || item.value.length < 10)"
        popper-class="dataview-custom-tooltip"
      >
        <template #content>
          <div v-html="generateTooltipHtml(item)"></div>
        </template>
        <div
          class="bg-[#092B5C] border border-[#1C63BC] p-3 flex justify-start items-start space-x-3 h-[70px]"
        >
          <div class="text-3xl w-10 h-10 flex items-center justify-center">
            <img :src="'/image/' + (iconMappings[item.name] || 'default_icon') + '.svg'" class="w-6 h-6" />
          </div>
          <div class="truncate flex-1">
            <div class="text-[14px] ">{{ item.title }}</div>
            <div class="text-[14px] text-[#2BC3F6] font-medium value-truncate">{{ item.value }}</div>
          </div>
        </div>
      </el-tooltip>
    </div>
    <div v-else class="flex justify-center items-center h-[200px]">
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  structureInfo: {
    type: Object,
    default: () => null,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const structureInfoForDisplay = computed(() => {
  if (!props.structureInfo) {
    return []
  }
  const info = props.structureInfo

  const formatDate = (dateStr) => {
    if (!dateStr) return '暂无数据'
    const s = String(dateStr)
    if (s.length !== 8) return s
    return `${s.substring(0, 4)}年${s.substring(4, 6)}月${s.substring(6, 8)}日`
  }

  const formatValue = (val, unit = '') => (val ? `${val}${unit}` : '暂无数据')

  const commonFields = (maintenanceName, managementName, monitorPoints, technicalGrade, designGrade) => [
    {
      name: 'managementUnit',
      title: '管养单位',
      value: managementName || '暂无数据',
      tooltipCompanyName: info.managementAlias,
      tooltipContacts: info.managementUser && info.managementPhone ? [{ name: info.managementUser, phone: info.managementPhone }] : [],
    },
    {
      name: 'operationUnit',
      title: '运维单位',
      value: maintenanceName || '暂无数据',
      tooltipCompanyName: info.maintenanceName,
      tooltipContacts: info.maintenanceUser && info.maintenancePhone ? [{ name: info.maintenanceUser, phone: info.maintenancePhone }] : [],
    },
    { name: 'monitoringPoints', title: '监测点位', value: formatValue(monitorPoints, '个') },
    { name: 'technicalGrade', title: '技术等级', value: technicalGrade || '暂无数据' },
    // { name: 'designGrade', title: '养护等级', value: designGrade || '暂无数据' },
  ]

  let specificFields = []
  switch (info.structureType) {
    case 1: // Bridge
      specificFields = [
        { name: 'openingDate', title: '通车时间', value: formatDate(info.openingDate) },
        { name: 'structureStyle', title: '结构类型', value: info.structureStyle || '暂无数据' },
        { name: 'bridgeLength', title: '桥梁全长', value: formatValue(info.bridgeLength, '米') },
        { name: 'structureWidth', title: '桥梁宽度', value: formatValue(info.structureWidth, '米') },
        ...commonFields(info.maintenanceName, info.managementName, info.monitorPoints, info.technicalGrade, info.designGrade),
      ]
      break
    case 2: // Tunnel
      specificFields = [
        { name: 'tunnelOpeningDate', title: '通车时间', value: formatDate(info.tunnelOpeningDate) },
        { name: 'structureStyle', title: '结构类型', value: info.structureStyle || '暂无数据' },
        { name: 'tunnelLength', title: '隧道全长', value: formatValue(info.tunnelLength, '米') },
        { name: 'tunnelWidth', title: '隧道宽度', value: formatValue(info.tunnelWidth, '米') },
        ...commonFields(info.maintenanceName, info.managementName, info.monitorPoints, info.technicalGrade, info.designGrade),
      ]
      break
    case 3: // Slope
      specificFields = [
        { name: 'slopeType', title: '边坡类型', value: info.slopeType || '暂无数据' },
        { name: 'slopeHeight', title: '边坡高度', value: formatValue(info.slopeHeight, '米') },
        { name: 'slopeWidth', title: '边坡宽度', value: formatValue(info.slopeWidth, '米') },
        { name: 'monitorScenarios', title: '监测场景', value: info.monitorScenarios || '暂无数据' },
        ...commonFields(info.maintenanceName, info.managementName, info.monitorPoints, info.technicalGrade, info.designGrade),
      ]
      break
    case 4: // Underpass
      specificFields = [
        { name: 'openingDate', title: '通车时间', value: formatDate(info.openingDate) },
        { name: 'underpassLength', title: '下穿通道长度', value: formatValue(info.underpassLength, '米') },
        { name: 'crossingStructure', title: '穿越结构物', value: info.crossingStructure || '暂无数据' },
        ...commonFields(info.maintenanceName, info.managementName, info.monitorPoints, null, info.designGrade), // technicalGrade not specified for underpass
      ]
      break
  }

  return specificFields
})

const iconMappings = {
  openingDate: 'icon-clock-time',
  structureStyle: 'icon-structure-bars',
  bridgeLength: 'icon-width-arrows',
  structureWidth: 'icon-width-arrows',
  managementUnit: 'icon-store-building',
  operationUnit: 'icon-store-building',
  monitoringPoints: 'icon-network-branch',
  technicalGrade: 'icon-layers-stacked',
  tunnelOpeningDate: 'icon-clock-time',
  tunnelLength: 'icon-width-arrows',
  tunnelWidth: 'icon-width-arrows',
  slopeType: 'icon-structure-bars',
  slopeHeight: 'icon-layers-stacked',
  slopeWidth: 'icon-width-arrows',
  monitorScenarios: 'icon-network-branch',
  underpassLength: 'icon-width-arrows',
  crossingStructure: 'icon-bridge-parenthesis',
  designGrade: 'icon-layers-stacked' // Kept for backward compatibility if needed
}

function generateTooltipHtml(item) {
  if (
    (item.name === 'managementUnit' || item.name === 'operationUnit') &&
    item.tooltipCompanyName &&
    item.tooltipContacts &&
    item.tooltipContacts.length > 0
  ) {
    const companyNameHtml = `<div>${item.tooltipCompanyName}</div>`
    const contactsHtml = item.tooltipContacts.map((contact) => `${contact.name}(${contact.phone})`).join('&nbsp;&nbsp;')
    return `<div>${companyNameHtml}<div style="color: #2BC3F6;">${contactsHtml}</div></div>`
  } else {
    return `<div>${item.value}</div>`
  }
}
</script>

<style>
.dataview-custom-tooltip {
  background-color: #051e3a !important;
  color: white !important;
  padding: 8px 10px !important;
  border-radius: 4px !important;
  max-width: 300px !important;
  word-break: break-all !important;
  z-index: 1000 !important;
  pointer-events: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid #1c63bc !important;
}

.dataview-custom-tooltip .el-popper__arrow::before {
  background: #051e3a !important;
  border-color: #1c63bc !important;
}

.value-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>