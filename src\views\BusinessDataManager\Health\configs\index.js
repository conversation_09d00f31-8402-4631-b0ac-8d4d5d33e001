// 健康度管理配置

// 管养单位选项
const managementOptions = [
  { label: '宁波大通开发有限公司', value: '宁波大通开发有限公司' },
  { label: '象山县公路与运输管理中心', value: '象山县公路与运输管理中心' },
  { label: '宁波绕城东段高速公路有限公司', value: '宁波绕城东段高速公路有限公司' },
  { label: '宁海县公路与运输管理中心', value: '宁海县公路与运输管理中心' },
  { label: '海曙区公路与运输管理中心', value: '海曙区公路与运输管理中心' },
  { label: '江北区公路与运输管理中心', value: '江北区公路与运输管理中心' }
]

// 整体健康度选项
const healthLevelOptions = [
  { label: '基本完好', value: 0 },
  { label: '轻微异常', value: 1 },
  { label: '中等异常', value: 2 },
  { label: '严重异常', value: 3 }
]

// 表格列配置
const searchColumns = [
  {
    label: '结构物名称',
    prop: 'structureName',
    search: true,
    overHidden: true,
    placeholder: '请输入结构物名称'
  },
  {
    label: '管养单位',
    prop: 'managementName',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: managementOptions,
    placeholder: '请选择管养单位'
  },
  {
    label: '评估时间',
    prop: 'evaluateTime',
    search: true,
    overHidden: true,
    type: 'datetime',
    format: 'YYYY-MM-DD HH:mm:ss',
    valueFormat: 'x', // 时间戳格式
    placeholder: '请选择评估时间范围',
    searchRange: true,
    searchSpan: 12,
    // 格式化显示时间戳
    formatter: (_, __, cellValue) => {
      if (cellValue) {
        const date = new Date(parseInt(cellValue))
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      }
      return ''
    }
  }, {
    label: '整体健康度',
    prop: 'entireHealthLevel',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: healthLevelOptions,
    placeholder: '请选择整体健康度'
  },
].map(i => ({ ...i, labelWidth: 120, searchLabelWidth: 100 }))

// 表格配置
export const healthOption = {
  column: searchColumns,
  editBtn: false,   // 没有编辑功能
  viewBtn: false,   // 没有查看功能
  addBtn: false,    // 没有添加功能
  delBtn: false,    // 没有删除功能
  menu: false,     // 隐藏操作列
  searchMenuSpan: 18
}

// 搜索表单初始值
export const healthSearchForm = {
  structureName: '',
  managementName: '',
  entireHealthLevel: '',
  timeRanges: []
}

// 导出选项数据供其他地方使用
export {
  managementOptions,
  healthLevelOptions
}
