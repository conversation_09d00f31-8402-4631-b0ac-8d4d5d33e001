<template>
  <AvTable
    :option="bridgeOption"
    :searchForm="bridgeSearchForm"
    :listApi="queryBridges"
    :detailApi="querySingleStructureInfo"
    :updateApi="addAndUpdate"
    @search="handleCustomSearch"
    @load="handleCustomLoad"
    @update="handleCustomUpdate"
  />
</template>

<script setup>
import AvTable from '../AvTable.vue'
import { bridgeOption, bridgeSearchForm } from './configs/bridge-config.js'
import { queryBridges, querySingleStructureInfo, addAndUpdate } from '/@/api/dataview/structure'

// 自定义搜索处理（可选）
function handleCustomSearch(params) {
  console.log('桥梁搜索参数:', params)
  // 可以在这里添加特殊的搜索逻辑
}

// 自定义加载处理（可选）
function handleCustomLoad() {
  console.log('桥梁数据加载')
  // 可以在这里添加特殊的加载逻辑
}

// 自定义更新处理（可选）
function handleCustomUpdate(formData) {
  console.log('桥梁更新数据:', formData)
  // 可以在这里添加特殊的更新逻辑
}
</script>
