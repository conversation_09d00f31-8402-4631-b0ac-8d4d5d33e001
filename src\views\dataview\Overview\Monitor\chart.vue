<script setup>
import VChart from 'vue-echarts'
import * as echarts from 'echarts'

const props = defineProps({
  intactRate: {
    type: Number,
    default: 85,
  },
});

let max = 100;
let value = computed(() => {
  return props.intactRate.toFixed(0);
})
const option = computed(() => ({
  // backgroundColor: '#0A193E', // Dark blue background, similar to the image
  title: {
    text: `${value.value}%`,
    show: true,
    x: 'center',
    y: 'center',
    textStyle: {
      fontSize: 18, // Adjust size as needed, using pixels for consistency
      color: 'white',
      fontFamily: 'Arial, Helvetica, sans-serif', // A common clean sans-serif font
      fontWeight: 'normal', // Text in image appears to be normal weight
    },
  },
  polar: {
    center: ['50%', '50%'],
    radius: ['58%', '65%'], // Defines the main progress bar ring. Thickness is 7% of radius.
  },
  angleAxis: {
    max: max,
    startAngle: 90, // Start from the top (12 o'clock)
    show: false,    // Hide the angle axis lines and labels
    clockwise: false, // Progress clockwise
  },
  radiusAxis: {
    type: 'category',
    show: false, // Hide the radius axis
    data: ['progress'], // A single category for the bar
  },
  series: [
    // Series 0: Main Progress Bar
    {
      name: 'Progress',
      type: 'bar',
      coordinateSystem: 'polar',
      // Data for the 'progress' category on radiusAxis
      // ECharts maps the single value in the array to the first category in radiusAxis.data
      data: [value.value],
      roundCap: true, // Makes the ends of the bar rounded
      showBackground: true,
      backgroundStyle: {
        color: '#1E606A', // Color of the unfilled part (track) - a darker teal
      },
      itemStyle: {
        color: '#30D5C8', // Color of the filled part - vibrant turquoise/teal
      },
      z: 2, // Ensure progress bar is above border rings
      markPoint: {
        animation: false, // No animation for the marker
        silent: true,     // Marker is not interactive
        data: [
          {
            // Position the marker at the end of the bar
            // [angleAxisValue, radiusAxisCategoryNameOrIndex]
            coord: [value.value, 'progress'],
            symbol: 'circle', // Shape of the marker
            symbolSize: 14,   // Diameter of the central white part of the dot
            itemStyle: {
              color: 'white', // Central part of the dot is white
              borderColor: '#30D5C8', // Border color matches the progress bar fill
              borderWidth: 2,        // Thickness of the border around the white dot
                                     // Total diameter of (white dot + border) = 14 + 2*2 = 18px
              shadowColor: '#30D5C8', // Glow color, same as progress bar fill
              shadowBlur: 8,         // Blur radius for the glow effect
              shadowOffsetX: 0,      // No horizontal shadow offset
              shadowOffsetY: 0,      // No vertical shadow offset
            },
          },
        ],
      },
    },

    // Series 1: Outer Border Ring (decorative)
    {
      name: 'Outer Border',
      type: 'pie',
      silent: true,          // Not interactive
      z: 1,                  // Behind the main progress bar
      radius: ['65.2%', '66.2%'], // Thin ring just outside the progress bar (1% thickness)
      itemStyle: {
        color: '#154850', // Dark, desaturated teal/blue for a subtle border
      },
      label: { show: false }, // No labels
      data: [{ value: 100, name: '' }], // Single segment to make a full ring
    },

    // Series 2: Inner Border Ring (decorative)
    {
      name: 'Inner Border',
      type: 'pie',
      silent: true,          // Not interactive
      z: 1,                  // Behind the main progress bar
      radius: ['56.8%', '57.8%'], // Thin ring just inside the progress bar (1% thickness)
      itemStyle: {
        color: '#154850', // Same color as the outer border
      },
      label: { show: false }, // No labels
      data: [{ value: 100, name: '' }], // Single segment
    },
  ],
}))


</script>

<template>
  <v-chart :option="option" autoresize />
</template>
