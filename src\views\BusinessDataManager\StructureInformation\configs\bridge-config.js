// 桥梁管理配置

// 导入共享选项数据
import {
  bridgeSceneOptions as sceneOptions,
  techStatusOptions,
  designGradeOptions,
  crossingObjectOptions
} from './common-options.js'

// 导入配置工厂函数
import { createStructureConfig } from './config-factory.js'

// 桥梁特有的结构数据字段
const bridgeStructureFields = [
  { label: '桥梁类型', prop: 'structureStyle', placeholder: '请输入桥梁类型' },
  { label: '桥梁长度', prop: 'bridgeLength', placeholder: '请输入桥梁长度' },
  { label: '桥梁宽度', prop: 'structureWidth', placeholder: '请输入桥梁宽度' },
  { label: '桥梁跨径', prop: 'bridgeSpan', placeholder: '请输入桥梁跨径' },
  {
    label: '技术等级',
    prop: 'technicalGrade',
    type: 'select',
    dicData: techStatusOptions,
    placeholder: '请选择技术等级'
  },
  {
    label: '养护等级',
    prop: 'designGrade',
    type: 'select',
    dicData: designGradeOptions,
    placeholder: '请选择养护等级'
  },
  {
    label: '跨越地物类型',
    prop: 'crossingObjectType',
    type: 'select',
    dicData: crossingObjectOptions,
    placeholder: '请选择跨越地物类型'
  },
  { label: '跨越地物名称', prop: 'crossingObjectName', placeholder: '请输入跨越地物名称' },
  {
    label: '图片',
    prop: 'imageUrl',
    type: 'upload',
    listType: 'picture-img',
    editDisabled: true,
    placeholder: '请输入图片'
  }
]

// 使用配置工厂创建桥梁配置
const bridgeConfig = createStructureConfig({
  structureType: '桥梁',
  monitorScenariosConfig: { dicData: sceneOptions },
  baseDataOptions: {
    additionalFields: [
      { label: '监测场景', prop: 'monitorScenarios', type: 'select', dicData: sceneOptions, placeholder: '请选择监测场景' }
    ],
    customFields: {
      structureUniqueCode: { label: '桥梁编号', placeholder: '请输入桥梁编号' },
      structureLocation: { label: '桥梁位置', placeholder: '请输入桥梁位置' }
    }
  },
  structureDataFields: bridgeStructureFields
})

// 导出桥梁表格配置
export const bridgeOption = bridgeConfig.tableConfig

// 导出搜索表单初始值
export const bridgeSearchForm = bridgeConfig.searchForm
