<template>
  <div class="w-full h-full p-4 bg-gray-50">
    <avue-crud 
      class="border p-4 bg-white"
      v-model:search="search"
      v-model="form"
      v-model:page="page"
      @search-change="handleSearch"
      :before-open="handleBeforeOpen"
      @onLoad="handleLoad"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @row-update="handleRowUpdate"
      :data="tableData"
      :option="computedOption"
      v-bind="$attrs"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

const props = defineProps({
  // 基础配置
  option: { type: Object, required: true },
  searchForm: { type: Object, default: () => ({}) },

  // API配置 - 更灵活的方式
  listApi: { type: Function, required: true },
  detailApi: { type: Function },
  updateApi: { type: Function },

  // 详情参数获取函数 - 支持动态参数
  getDetailParams: { type: Function, default: () => 330007 },

  // 自定义处理函数
  customSearch: { type: Function },
  customUpdate: { type: Function },
})

const emit = defineEmits(['search', 'load', 'update', 'beforeOpen'])

const page = ref({ total: 1000, currentPage: 1, pageSize: 10 })
const form = ref({})
const search = reactive({ ...props.searchForm })
const tableData = ref([])
const loading = ref(false)

// 合并默认配置和传入配置
const computedOption = computed(() => ({
  border: true,
  viewBtn: true,
  stripe: true,
  delBtn: false,
  menu: true,
  addBtn: false,
  refreshBtn: false,
  index: true,
  columnBtn: false,
  header: false,
  align: 'center',
  searchMenuPosition: 'right',
  searchMenuSpan: 18,
  ...props.option
}))

function handleSearch(params, done) {
  if (loading.value) {
    done()
    return
  }

  emit('search', params)

  // 如果有自定义搜索处理函数，优先使用
  if (props.customSearch) {
    props.customSearch(params, done, updateTableData)
    return
  }

  // 默认搜索处理
  loading.value = true
  props.listApi(params).then(res => {
    updateTableData(res)
    done()
  }).catch(err => {
    console.error('搜索失败:', err)
    done()
  }).finally(() => {
    loading.value = false
  })
}

function handleLoad() {
  if (loading.value) {
    return
  }

  emit('load')

  loading.value = true
  // 传递完整的分页和搜索参数
  const params = {
    ...search,
    current: page.value.currentPage,
    size: page.value.pageSize
  }
  console.log('加载参数:', params)

  props.listApi(params).then(res => {
    updateTableData(res)
  }).catch(err => {
    console.error('加载数据失败:', err)
  }).finally(() => {
    loading.value = false
  })
}

function handleBeforeOpen(done, type, loading) {
  emit('beforeOpen', done, type, loading)
  
  if (!props.detailApi) {
    done()
    return
  }
  
  props.detailApi(form.value.structureUniqueCode).then(res => {
    form.value = res.data
    done()
  }).catch(err => {
    console.error('获取详情失败:', err)
    done()
  })
}

function handleRowUpdate(formData, index, done, loading) {
  emit('update', formData)
  
  // 如果有自定义更新处理函数，优先使用
  if (props.customUpdate) {
    props.customUpdate(formData, index, done, loading, handleLoad)
    return
  }
  
  // 默认更新处理
  if (!props.updateApi) {
    done()
    return
  }
  
  props.updateApi(formData).then(res => {
    handleLoad()
    done()
  }).catch(err => {
    console.error('更新失败:', err)
    done()
  })
}

function handleSizeChange(size) {
  if (loading.value) {
    return
  }

  page.value.pageSize = size
  // 切换页面大小时重置到第一页
  page.value.currentPage = 1

  const params = {
    ...search,
    size: size,
    current: 1
  }
  console.log('分页大小变更参数:', params)

  loading.value = true
  props.listApi(params).then(res => {
    updateTableData(res)
  }).catch(err => {
    console.error('分页大小变更失败:', err)
  }).finally(() => {
    loading.value = false
  })
}

function handleCurrentChange(current) {
  if (loading.value) {
    return
  }

  page.value.currentPage = current

  const params = {
    ...search,
    current: current,
    size: page.value.pageSize
  }
  console.log('页码变更参数:', params);

  loading.value = true
  props.listApi(params).then(res => {
    updateTableData(res)
  }).catch(err => {
    console.error('页码变更失败:', err)
  }).finally(() => {
    loading.value = false
  })
}

// 统一的数据更新函数
function updateTableData(res) {
  tableData.value = res.data.records
  page.value = {
    total: res.data.total,
    currentPage: res.data.current,
    pageSize: res.data.size
  }
}
</script>
