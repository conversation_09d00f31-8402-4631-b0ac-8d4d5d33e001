// 配置文件统一导出

// 导出共享选项数据
export * from './common-options.js'

// 导出配置工厂函数
export * from './config-factory.js'

// 导出桥梁配置
export { bridgeOption, bridgeSearchForm, sceneOptions as bridgeSceneOptions } from './bridge-config.js'

// 导出边坡配置
export { slopeOption, slopeSearchForm } from './slope-config.js'

// 导出隧道配置
export { tunnelOption, tunnelSearchForm } from './tunnel-config.js'

// 导出下穿通道配置
export { underpassOption, underpassSearchForm } from './underpass-config.js'

// 重新导出边坡特有的选项（为了向后兼容）
export {
  slopeSceneOptions,
  slopeTypeOptions,
  riskLevelOptions,
  supportMethodOptions
} from './common-options.js'

// 重新导出下穿通道特有的选项
export {
  underpassSceneOptions,
  crossingStructureOptions
} from './underpass-config.js'
