<template>
  <AvTable
    :option="healthOption"
    :searchForm="healthSearchForm"
    :listApi="getHealthInfoList"
    :customSearch="handleCustomSearch"
    @search="handleSearch"
    @load="handleLoad"
  />
</template>

<script setup>
import AvTable from '../AvTable.vue'
import { healthOption, healthSearchForm } from './configs/index.js'
import { getHealthInfoList } from '/@/api/dataview/health'

// 自定义搜索处理
function handleCustomSearch(params, done, updateTableData) {
  console.log('健康度搜索参数:', params)

  // 处理时间范围参数
  const searchParams = { ...params }
  if (params.timeRanges && params.timeRanges.length === 2) {
    // 将时间范围转换为后端需要的格式
    searchParams.timeRanges = [params.timeRanges[0], params.timeRanges[1]]
  }

  getHealthInfoList(searchParams).then(res => {
    // 适配AvTable期望的数据格式
    const adaptedRes = {
      data: {
        records: res.data || [],
        total: (res.data || []).length,
        current: params.current || 1,
        size: params.size || 10
      }
    }
    updateTableData(adaptedRes)
    done()
  }).catch(err => {
    console.error('搜索失败:', err)
    done()
  })
}

// 搜索事件处理
function handleSearch(params) {
  console.log('搜索事件:', params)
}

// 加载事件处理
function handleLoad() {
  console.log('加载事件')
}
</script>
