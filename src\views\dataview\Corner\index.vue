<script setup lang='ts'>
</script>

<template>
  <div class="w-[95%] h-[95%] absolute corner-lines-effect"></div>

</template>

<style scoped>
.corner-lines-effect {
  /* 将所有 rgb(60,255,253) 替换为 #134DCE */
  background: linear-gradient(to left, #134DCE, #134DCE) left top no-repeat,
    linear-gradient(to bottom, #134DCE, #134DCE) left top no-repeat,
    linear-gradient(to left, #134DCE, #134DCE) right top no-repeat,
    linear-gradient(to bottom, #134DCE, #134DCE) right top no-repeat,
    linear-gradient(to left, #134DCE, #134DCE) left bottom no-repeat,
    linear-gradient(to bottom, #134DCE, #134DCE) left bottom no-repeat,
    linear-gradient(to left, #134DCE, #134DCE) right bottom no-repeat,
    linear-gradient(to left, #134DCE, #134DCE) right bottom no-repeat;
  /* 更新 background-size 来控制长度 (这里假设改为 40px) 和厚度 (保持 3px) */
  /* 垂直线尺寸是 3px x 40px，水平线尺寸是 40px x 3px */
  background-size: 3px 10px, 10px 3px, 3px 10px, 10px 3px;
}
</style>