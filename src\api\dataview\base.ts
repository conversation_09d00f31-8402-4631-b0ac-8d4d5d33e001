import request from '/@/utils/request';
import { dataview } from '/@/stores/dataview'; // 导入 Pinia store

// 辅助函数，用于创建包含通用 routeClassify 参数的 API 请求
// T_SpecificParams 用于定义每个 API 特有的参数类型
function createDataViewApiCall<T_SpecificParams extends Record<string, any>>(
  url: string,
  specificParams?: T_SpecificParams, // API 特有的参数，可选
  method: string = 'post' // 默认为 'post'，可以按需修改
) {
  const store = dataview(); // 获取 Pinia store 实例
  const routeClassifyFromStore = store.roadFilter; // 从 store 中获取 roadFilter

  // 准备请求体数据
  // routeClassifyFromStore 会作为基础，specificParams 中的同名属性会覆盖它（如果存在）
  const requestData = {
    routeClassify: routeClassifyFromStore,
    ...(specificParams || {}), // 合并 API 特有的参数
  };

  return request({
    url: url,
    method: method,
    data: requestData,
  });
}

//  /hwm/cbmsData/getBridgeAndTunnelTechnicalCondition
export const getBridgeAndTunnelTechnicalCondition = () => {
  return createDataViewApiCall('/hwm/cbmsData/getBridgeAndTunnelTechnicalCondition');
};
// /cbmsData/getBridgeAndTunnelMaintenanceOverview
export const getBridgeAndTunnelMaintenanceOverview = ()=>{
  return createDataViewApiCall('/hwm/cbmsData/getBridgeAndTunnelMaintenanceOverview')
}

// /structure/getStructureData
export const getStructureData = ()=>{
  return createDataViewApiCall('/hwm/structure/getStructureData')
}
// /alarm/getAlarmData
export const getAlarmData = ()=>{
  return createDataViewApiCall('/hwm/alarm/getAlarmData')
}
// /alarm/getWarningStats
export const getWarningStats = ()=>{
  return createDataViewApiCall('/hwm/alarm/getWarningStats')
}

// /structure/getMonitorOverview
export const getMonitorOverview = ()=>{
  return createDataViewApiCall('/hwm/structure/getMonitorOverview')
}
// /structure/getHealthCondition
export const getHealthCondition = ()=>{
  return createDataViewApiCall('/hwm/structure/getHealthCondition')
}
// /device/getDeviceListStatus
export const getDeviceListStatus = ()=>{
  return createDataViewApiCall('/hwm/device/getDeviceListStatus')
}
// /structure/getMonitorResult
export const getMonitorResult = () => {
  return createDataViewApiCall('/hwm/structure/getMonitorResult');
};
// /structure/getComprehensiveIndex
export const getComprehensiveIndex = ()=>{
  return createDataViewApiCall('/hwm/structure/getComprehensiveIndex')
}