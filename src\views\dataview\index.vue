<script setup lang='ts'>
import 'animate.css';
import { NextLoading } from '/@/utils/loading';
import autofit from 'autofit.js'
import Header from './Header/index.vue'
import MapContainer from './MapContainer/index.vue'
import Overview from './Overview/index.vue'
import { dataview } from '/@/stores/dataview'
import { useThemeConfig } from '/@/stores/themeConfig';
import { ElMessageBox } from 'element-plus';
import screenfull from 'screenfull';

const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const beforeTheme = themeConfig.value.isDark
const body = document.documentElement as HTMLElement;
if (!beforeTheme) {
  body.setAttribute('data-theme', 'dashboard');
  body.classList.add('dashboard');
}
onBeforeUnmount(() => {
  themeConfig.value.isDark = beforeTheme
  if (!beforeTheme) {
    body.classList.remove('dashboard');
    body.setAttribute('data-theme', '');
  }
})
const MonitorWarning =defineAsyncComponent(()=>import('./MonitorWarning/index.vue'))
const Structure =defineAsyncComponent(()=>import('./Structure/index.vue'))

const pages = {  Overview,MonitorWarning,Structure}
const { curPage } = storeToRefs(dataview())

// 全屏提示功能
const showFullscreenTip = () => {
  // 检查用户是否已经选择过不再提示
  const dontShowAgain = localStorage.getItem('dataview-fullscreen-tip-dismissed');

  // 检查是否已经是全屏状态或用户已选择不再提示
  if (screenfull.isEnabled && !screenfull.isFullscreen && !dontShowAgain) {
    ElMessageBox({
      title: '全屏提示',
      message: '为了获得最佳的数据视图体验，建议您使用全屏模式。',
      type: 'info',
      center: true,
      customClass: 'fullscreen-tip-dialog',
      showCancelButton: true,
      showClose: true,
      confirmButtonText: '进入全屏',
      cancelButtonText: '不再提示',
      distinguishCancelAndClose: true,
      beforeClose: (action, _instance, done) => {
        if (action === 'confirm') {
          // 用户点击确定，进入全屏
          if (screenfull.isEnabled) {
            screenfull.request();
          }
        } else if (action === 'cancel') {
          // 用户点击"不再提示"
          localStorage.setItem('dataview-fullscreen-tip-dismissed', 'true');
        }
        // 关闭对话框
        done();
      }
    });
  }
};

// 重置全屏提示状态（开发者工具函数）
const resetFullscreenTip = () => {
  localStorage.removeItem('dataview-fullscreen-tip-dismissed');
  console.log('全屏提示状态已重置，刷新页面后将重新显示提示');
};

// 在开发环境下将函数暴露到全局，方便调试
if (import.meta.env.DEV) {
  (window as any).resetFullscreenTip = resetFullscreenTip;
}

onMounted(() => {
  NextLoading.done(0);
  autofit.init({    el: '#dataview', limit: 0.01  });

  // 页面加载完成后延迟显示全屏提示
  setTimeout(() => {
    showFullscreenTip();
  }, 1000);
})

</script>

<template>
  <main id="dataview">
    <div class='w-full h-full bg-black grid place-items-center text-white'>
      <div class="w-[1920px] h-[1080px] relative">
        <Header />
        <main class="relative top-[88px] h-[992px] w-full">
          <!-- center1 -->
          <MapContainer v-show="curPage !== 'Structure'" />
            <keep-alive>
              <component :is="pages[curPage]" />
            </keep-alive>
        </main>
      </div>
    </div>
  </main>
</template>

<style scoped>
.custom-radio-group :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #258EFF; /* 浅蓝色背景 */
  border-color: #258EFF;
  color: #fff; /* 选中时深色字体 */
  box-shadow: -1px 0 0 0 #258EFF;
}

.custom-radio-group :deep(.el-radio-button__inner) {
  color: #fFF; /* 默认状态浅蓝色字体 */
  background-color: rgba(29, 155, 240, 0);
  border-color: #2E76C3;
}

/* 全屏提示对话框样式 */
:global(.fullscreen-tip-dialog) {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

:global(.fullscreen-tip-dialog .el-message-box__header) {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #ffffff;
}

:global(.fullscreen-tip-dialog .el-message-box__title) {
  font-size: 18px;
  font-weight: 600;
  color: #39d8fb;
}

:global(.fullscreen-tip-dialog .el-message-box__content) {
  padding: 20px 24px;
  font-size: 14px;
  line-height: 1.6;
  color: #d3d8e2;
}

:global(.fullscreen-tip-dialog .el-message-box__btns) {
  padding: 16px 24px 24px;
  text-align: right;
}

:global(.fullscreen-tip-dialog .el-button--primary) {
  background: linear-gradient(135deg, #258EFF 0%, #1976D2 100%);
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:global(.fullscreen-tip-dialog .el-button--primary:hover) {
  background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 142, 255, 0.3);
}

:global(.fullscreen-tip-dialog .el-button--default) {
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}
</style>

