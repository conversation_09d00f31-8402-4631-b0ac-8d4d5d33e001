// 下穿通道管理配置

// 导入共享选项数据
import {
  techStatusOptions,
  designGradeOptions,
  crossingObjectOptions
} from './common-options.js'

// 导入配置工厂函数
import { createStructureConfig } from './config-factory.js'

// 下穿通道监测场景选项
const underpassSceneOptions = [
  { label: '结构健康监测', value: '结构健康监测' },
  { label: '变形监测', value: '变形监测' },
  { label: '应力监测', value: '应力监测' },
  { label: '环境监测', value: '环境监测' },
  { label: '安全监测', value: '安全监测' }
]

// 穿越结构物类型选项
const crossingStructureOptions = [
  { label: '铁路', value: '铁路' },
  { label: '高速公路', value: '高速公路' },
  { label: '国省道', value: '国省道' },
  { label: '城市道路', value: '城市道路' },
  { label: '河流', value: '河流' },
  { label: '管线', value: '管线' },
  { label: '其他', value: '其他' }
]

// 下穿通道特有的结构数据字段
const underpassStructureFields = [
  { 
    label: '下穿通道长度', 
    prop: 'underpassLength', 
    placeholder: '请输入下穿通道长度（米）',
    suffix: '米'
  },
  { 
    label: '穿越结构物', 
    prop: 'crossingStructure', 
    type: 'select', 
    dicData: crossingStructureOptions, 
    placeholder: '请选择穿越结构物类型' 
  },
  {
    label: '技术等级',
    prop: 'technicalGrade',
    type: 'select',
    dicData: techStatusOptions,
    placeholder: '请选择技术等级'
  },
  {
    label: '养护等级',
    prop: 'designGrade',
    type: 'select',
    dicData: designGradeOptions,
    placeholder: '请选择养护等级'
  },
  {
    label: '跨越地物类型',
    prop: 'crossingObjectType',
    type: 'select',
    dicData: crossingObjectOptions,
    placeholder: '请选择跨越地物类型'
  },
  { 
    label: '跨越地物名称', 
    prop: 'crossingObjectName', 
    placeholder: '请输入跨越地物名称' 
  },
  {
    label: '图片',
    prop: 'imageUrl',
    type: 'upload',
    listType: 'picture-img',
    editDisabled: true,
    placeholder: '请上传图片'
  }
]

// 使用配置工厂创建下穿通道配置
const underpassConfig = createStructureConfig({
  structureType: '下穿通道',
  monitorScenariosConfig: { dicData: underpassSceneOptions },
  baseDataOptions: {
    // 添加下穿通道特有的基础字段
    additionalFields: [
      { 
        label: '监测场景', 
        prop: 'monitorScenarios', 
        type: 'select', 
        dicData: underpassSceneOptions, 
        placeholder: '请选择监测场景' 
      }
    ],
    // 自定义字段标签
    customFields: {
      structureUniqueCode: { 
        label: '下穿通道编号', 
        placeholder: '请输入下穿通道编号' 
      },
      structureName: { 
        label: '下穿通道名称', 
        placeholder: '请输入下穿通道名称' 
      },
      structureLocation: { 
        label: '下穿通道位置', 
        placeholder: '请输入下穿通道位置' 
      }
    }
  },
  structureDataFields: underpassStructureFields,
  // 添加额外的搜索表单字段
  additionalSearchFormFields: {
    crossingStructure: ''
  }
})

// 导出下穿通道表格配置
export const underpassOption = underpassConfig.tableConfig

// 导出搜索表单初始值
export const underpassSearchForm = underpassConfig.searchForm

// 导出下穿通道特有的选项数据（供其他地方使用）
export { 
  underpassSceneOptions, 
  crossingStructureOptions 
}
