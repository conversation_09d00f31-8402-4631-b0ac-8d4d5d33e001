<template>
  <AvTable
    :option="slopeOption"
    :searchForm="slopeSearchForm"
    :listApi="querySlopes"
    :detailApi="querySingleStructureInfo"
    :updateApi="addAndUpdate"
    @search="handleCustomSearch"
    @load="handleCustomLoad"
    @update="handleCustomUpdate"
  />
</template>

<script setup>
import AvTable from '../AvTable.vue'
import { slopeOption, slopeSearchForm } from './configs/slope-config.js'
import { querySlopes, querySingleStructureInfo, addAndUpdate } from '/@/api/dataview/structure'

// 自定义搜索处理（可选）
function handleCustomSearch(params) {
  console.log('边坡搜索参数:', params)
  // 可以在这里添加特殊的搜索逻辑
  // 添加边坡类型过滤条件
  params.structureType = 3 // 假设边坡的结构类型为3
}

// 自定义加载处理（可选）
function handleCustomLoad() {
  console.log('边坡数据加载')
  // 可以在这里添加特殊的加载逻辑
}

// 自定义更新处理（可选）
function handleCustomUpdate(form) {
  console.log('边坡更新数据:', form)
  // 确保设置正确的结构类型
  form.structureType = 3 // 边坡类型
  // 可以在这里添加特殊的更新逻辑
}
</script>