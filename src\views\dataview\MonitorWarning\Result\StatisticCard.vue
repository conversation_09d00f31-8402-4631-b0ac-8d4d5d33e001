<template>
  <div class="relative pl-2 w-full h-full bg-[#051743]">
    <div class="h-full py-[0px] flex items-center justify-around border border-[#2068A1] flex-wrap">
      <span class="absolute left-0 mt-4 z border border-x-0 text-wrap w-4 border-[#2198D1] bg-[#114976] text-[#23A1D2]">{{ label }}</span>

      <div class="w-[120px] h-[90px] flex items-center justify-center">
        <div class="w-[72px] h-[72px]">
          <Rate :rate="rateValue" name="完好率" />
        </div>
      </div>

      <div class="flex-1 flex flex-col min-w-[115px] gap-1 h-20 px-2">
        <div v-for="(value, key) in statusData" :key="key" class="flex items-center px-2 flex-1 border border-[#2068A1] bg-[#081D4A]">
          <span class="text-gray-300 text-[14px] mr-3">{{ levelMap[key] }}:</span>
          <span class="text-[14px] font-bold" :style="{ color: getLevelColor(key, value) }">
            {{ value }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Rate from '../Statistics/Rate.vue'

// 定义级别到显示文本的映射
const levelMap = {
  normal: '基本完好',
  warning: '轻微离线',
  danger: '严重离线'
}

// 定义级别到颜色的映射函数
function getLevelColor(level, value) {
  switch (level) {
    case 'normal':
      return '#24A4D5' // 青色
    case 'warning':
      return value > 0 ? 'green' : '#24A4D5' // 如果有警告则绿色，否则青色
    case 'danger':
      return 'red' // 红色
    default:
      return '#24A4D5'
  }
}

// 接收props
const props = defineProps({
  label: String,
  rateValue: Number,
  statusData: Object,
})
</script>