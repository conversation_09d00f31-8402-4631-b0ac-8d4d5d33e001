<script setup>
import VChart from 'vue-echarts'
import * as echarts from 'echarts'
import Corner from '../../Corner/index.vue'
import { getAlarmData } from '/@/api/dataview/base'
import { useRoadFilterApi } from '/@/composables/useApi';
import { dataview } from '/@/stores/dataview';
const store = dataview();
const { data: apiData } = useRoadFilterApi(getAlarmData);

// 提取颜色常量
const COLORS = {
  // 环形图颜色
  PIE_DONE: '#2BCCFF',
  PIE_TODO: 'red',

  // 内圆渐变色
  INNER_CIRCLE_START: '#4DC3F2',
  INNER_CIRCLE_END: '#0B4D7F',

  // 指标卡颜色
  ALARM_CARD: {
    color1: '#155DB4',
    color2: '#258EFF'
  },
  DONE_CARD: {
    color1: '#127F7D',
    color2: '#1BC1A3'
  },
  TODO_CARD: {
    color1: '#821E3F',
    color2: '#D22340'
  },

  // 文字颜色
  TEXT_WHITE: '#FFFFFF',
  TEXT_BLUE: '#2BCCFF'
}


// 生成option的函数 (更健壮的版本)
const generateOption = (disposalRate, todoCount = 0, doneCount = 0, alarmCount = -1) => ({
  tooltip: {
    trigger: 'item',
    show: false
  },
  color: [COLORS.PIE_TODO, COLORS.PIE_DONE],
  graphic: [
    {
      type: 'text',
      left: 'center',
      top: '38%',
      style: {
        text: alarmCount === 0 ? '—' : `${Math.round((disposalRate || 0) * 100)}%`,
        textAlign: 'center',
        fontSize: 16,
        fontWeight: 'bold',
        fill: COLORS.TEXT_WHITE
      },
      z: 10
    }
  ],
  series: [
    {
      name: '处置状态',
      type: 'pie',
      radius: ['70%', '80%'],
      avoidLabelOverlap: false,
      padAngle: 1,
      itemStyle: {
        borderRadius: 10
      },
      label: {
        show: false,
      },
      labelLine: {
        show: false,
      },
      data: [
        { value: todoCount, name: '未处置' },
        { value: alarmCount === 0 ? 1 : doneCount, name: '已处置' },
      ]
    },
    {
      name: '內圆渐变色',
      type: 'pie',
      radius: [0, '62%'],
      itemStyle: {
        shadowBlur: 20,
        color: {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.62,
          colorStops: [
            {
              offset: 0,
              color: COLORS.INNER_CIRCLE_START,
            },
            {
              offset: 1,
              color: COLORS.INNER_CIRCLE_END,
            },
          ],
        },
        opacity: 1,
      },
      legendHoverLink: false,
      label: {
        show: false,
      },
      data: [100],
      z: 3,
    },
  ]
});

// 计算属性
const totalAlarm = computed(() => apiData.value?.["预警总数"] || 0);
const disposalRate = computed(() => apiData.value?.处置率 || 0);

// 获取各级预警数据的安全方法
const getLevelData = (level) => {
  const data = apiData.value?.[level] || {};
  return {
    alarmCount: data.预警数 || 0,
    doneCount: parseInt(data.已处置 || 0),
    todoCount: parseInt(data.未处置 || 0),
    disposalRate: data.处置率 || 0
  };
};

// 总处置率图表
const option = computed(() => {
  const rate = disposalRate.value || 0;
  const doneValue = rate * 100;
  const todoValue = 100 - doneValue;

  return generateOption(
    rate,
    todoValue,
    doneValue,
    totalAlarm.value
  );
});

// 二级预警处置率图表
const level2Option = computed(() => {
  const level2 = getLevelData("2级预警");
  return generateOption(level2.disposalRate, level2.todoCount, level2.doneCount, level2.alarmCount);
});

// 三级预警处置率图表
const level3Option = computed(() => {
  const level3 = getLevelData("3级预警");
  return generateOption(level3.disposalRate, level3.todoCount, level3.doneCount, level3.alarmCount);
});

// 卡片数据
const cardTypes = computed(() => [
  {
    ...COLORS.ALARM_CARD,
    label: '预警数',
    getValue: () => {
      const level2 = getLevelData("2级预警");
      const level3 = getLevelData("3级预警");
      return level2.alarmCount + level3.alarmCount;
    }
  },
  {
    ...COLORS.DONE_CARD,
    label: '已处置',
    getValue: () => {
      const level2 = getLevelData("2级预警");
      const level3 = getLevelData("3级预警");
      return level2.doneCount + level3.doneCount;
    }
  },
  {
    ...COLORS.TODO_CARD,
    label: '未处置',
    getValue: () => {
      const level2 = getLevelData("2级预警");
      const level3 = getLevelData("3级预警");
      return level2.todoCount + level3.todoCount;
    }
  }
]);
const handleClick = (item) => {
  const healthDegree ={
    基本完好: 0,
    轻微异常: 1,
    中等异常: 2,
    严重异常: 3
  }
  store.setFilterList([{ id: 'healthDegree', path: ['healthDegree'], value: healthDegree[item.label] }]);
};
</script>

<template>
  <div class="w-full h-[280px] text-white">
    <div class="w-full h-[70px] mb-4 flex items-center">
      <div class="alarm w-1/2 mx-6 h-full relative">
        <span class="text-white text-s absolute top-7 left-20">预警总数</span>
        <span class="text-[#2BCCFF] text-5xl absolute top-5 right-0">{{ totalAlarm }}</span>
      </div>
      <div class="w-1/2 h-full flex items-center">
        <div class="w-1/2 h-full">
          <VChart :option="option" autoresize />
        </div>
        <span>处置率</span>
      </div>
    </div>
    <div class="w-full h-[180px] relative flex justify-center items-center">
      <div class="w-[95%] h-[95%] border-2 border-[#0B2B7A] flex flex-wrap">
        <div class="w-1/2 h-10 flex justify-center items-center">
          <div class="flex items-center">
            <img src="/image/left.svg" /> <span class="text-white">2级预警</span><img src="/image/right.svg" />
          </div>
        </div>
        <div class="w-1/2 h-10 flex justify-center items-center">
          <div class="flex items-center">
            <img src="/image/left.svg" /> <span class="text-white">3级预警</span><img src="/image/right.svg" />
          </div>
        </div>

        <!-- 2级预警区域 -->
        <div class="w-1/2 h-[95px] flex">
          <div class="w-1/2 h-[80px] text-center">
            <VChart :option="level2Option" autoresize />
            <span class="text-white">处置率</span>
          </div>
          <div class="flex flex-col w-1/2 h-full justify-between">
            <div v-for="(card, index) in cardTypes" :key="index" class="">
              <div class="flex items-center gap-2">
                <div class="border-2 grid place-items-center w-2 h-2" :style="{ borderColor: card.color1 }">
                  <div class="w-1 h-1" :style="{ backgroundColor: card.color2 }"></div>
                </div>
                <span>{{ card.label }}</span>
                <span>{{ getLevelData('2级预警')[card.label === '预警数' ? 'alarmCount' :
                  card.label === '已处置' ? 'doneCount' : 'todoCount'] }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 3级预警区域 -->
        <div class="w-1/2 h-[95px] flex">
          <div class="w-1/2 h-[80px] text-center">
            <VChart :option="level3Option" autoresize />
            <span class="text-white">处置率</span>
          </div>
          <div class="flex flex-col w-1/2 h-full justify-between">
            <div v-for="(card, index) in cardTypes" :key="index" class="">
              <div class="flex items-center gap-2">
                <div class="border-2 grid place-items-center w-2 h-2" :style="{ borderColor: card.color1 }">
                  <div class="w-1 h-1" :style="{ backgroundColor: card.color2 }"></div>
                </div>
                <span>{{ card.label }}</span>
                <span>{{ getLevelData('3级预警')[card.label === '预警数' ? 'alarmCount' :
                  card.label === '已处置' ? 'doneCount' : 'todoCount'] }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Corner />
    </div>
  </div>
</template>

<style scoped>
.alarm {
  background-image: url('/image/Group <EMAIL>');
  background-size: cover;
}
</style>