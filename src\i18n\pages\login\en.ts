export default {
	label: {
		one1: 'Account login',
		two2: 'SMS login',
		three3: 'Social login',
		register: 'Create account',
	},
	link: {
		one3: 'Third party login',
		two4: 'Links',
	},
	password: {
		accountPlaceholder1: 'The username "admin" is common, please use another',
		accountPlaceholder2: 'Password',
		accountPlaceholder3: 'Please enter the verification code',
		phonePlaceholder4: 'Please enter your phone number',
		accountBtnText: 'Sign in',
		resetBtnText: 'Reset',
		registerBtnText: 'Create account',
		readAccept: 'I have read and accept',
		privacyPolicy: 'the Privacy Policy',
		oldPassword: 'Please enter old password',
		newPassword: 'Please enter new password',
		confirmPassword: 'Please confirm new password',
		backToLogin: 'Back to login',
		mobileLogin: 'Mobile Login',
		createAccount: 'Create Account',
		imageCodeTip: 'Please enter code',
	},
	mobile: {
		placeholder1: 'Please enter your mobile number',
		placeholder2: 'Please enter the verification code',
		codeText: 'Get code',
		btnText: 'Sign in',
		mobileLogin: 'Mobile Login',
		backToLogin: 'Back to login',
		createAccount: 'Create Account',
		sendSuccess: 'Verification code sent successfully',
		seconds: 'seconds to resend',
		mobileRequired: 'Please enter your mobile number',
		codeRequired: 'Please enter verification code',
		codeLength: 'Verification code must be 4 digits',
		sendFailed: 'Failed to send verification code',
		loginSuccess: 'Login successful',
		loginFailed: 'Login failed',
		signIn: 'Sign In',
	},
	scan: {
		text: 'Use your mobile device to scan and quickly log in or create an account',
		wechatApp: 'Scan with WeChat',
		appErrorTip: 'The login fails because the application is not configured',
	},
	signInText: 'Welcome back!',
	browserMsgText:
		'Tip: For best results, we recommend using Google Chrome, Microsoft Edge (version 80 or higher), or the 360 browser with speed mode enabled.',
	expire: {
		oldPassword: 'Please enter old password',
		newPassword: 'Please enter new password',
		confirmPassword: 'Please confirm new password',
		passwordRule: 'The two passwords do not match',
		passwordScore: 'Password strength is too low',
		resetSuccess: 'Password reset successfully',
	},
	tenantSelect: {
		select: 'Select Tenant',
		loadError: 'Failed to get tenant list',
	},
	verify: {
		imageCode: 'Please enter verification code',
	},
	register: {
		usernameEmpty: 'Username cannot be empty',
		usernameLength: 'Username length must be between 5 and 20 characters',
		phoneEmpty: 'Phone number cannot be empty',
		passwordEmpty: 'Password cannot be empty',
		passwordLength: 'Password length must be between 6 and 20 characters',
		passwordStrength: 'Password strength is too low',
		termsRequired: 'Please read and accept the terms',
	},
	divider: {
		or: 'or',
	},
	socialLogin: {
		wechatWork: 'WeCom',
		dingtalk: 'DingTalk',
	},
};
