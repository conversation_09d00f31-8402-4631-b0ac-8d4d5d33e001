<script setup>
import VChart from 'vue-echarts';
import * as echarts from 'echarts';
const props = defineProps({
  onlineRate: {
    type: Number,
    default: 100,
  },
});

const max = 100;
const fontSize = 8;

// 正确的响应式计算
const value = computed(() => props.onlineRate.toFixed(0));
const rate = computed(() => Math.round((props.onlineRate * 100) / max));
const option = computed(() => ({
  backgroundColor: '#0A183C', // Adjusted to match image background
  title: [
    {
      text: ` {a|${rate.value}%}\n{b|在线率}`, // Changed text to "在线率" as in image
      show: true,
      x: 'center',
      y: 'center',
      textStyle: {
        rich: {
          a: { fontSize:(fontSize*2), color: 'white', },
          b: { fontSize:fontSize, color: 'white', padding: [0, 0, 0, 0], },
        },
      },
    },
  ],
  polar: {
    center: ['50%', '50%'],
    radius: ['50%', '65%'], // Defines the space for the main progress bar
  },
  angleAxis: {
    max: max,
    show: false, clockwise: false, // clockwise: false ensures 0 is at the top and progresses clockwise if startAngle is default -90
  },
  radiusAxis: {
    type: 'category',
    show: true,
    axisLabel: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
  },
  series: [
    {
      // Main Progress Bar
      name: 'Progress',
      type: 'bar',
      roundCap: true,
      showBackground: true,
      backgroundStyle: {
        color: '#104A7C', // New: Darker blue track, matching image
      },
      data: [value.value],
      coordinateSystem: 'polar',
      itemStyle: {
          color: '#27BDF0', // Existing: Bright cyan fill, seems good
      },
      z: 2, // Ensure progress bar is above background rings
    },
    {
      // Inner Gradient Circle
      name: '內圆渐变色',
      type: 'pie',
      radius: [0, '49%'], // Sits inside the polar/bar radius
      itemStyle: {
        shadowBlur: 20, // Kept from original, adds a bit of softness
        color: {
            type: 'radial',
            x: 0.5,
            y: 0.5,
            r: 0.45, // Defines how far the center color spreads
            colorStops: [
              {
                offset: 0,
                color: '#4DC3F2', // New: Brighter, more luminous blue center
              },
              {
                offset: 1,
                color: '#0B4D7F', // New: Medium-dark blue edge for the gradient
              },
            ],
          },
          opacity:1,
      },
      legendHoverLink: false,
      label: {
        show: false,
      },
      data: [100],
      z: 3, // Highest z-index to be on top
    },
    {
      // Outer thin border ring
      name: 'Outer Border',
      type: 'pie',
      legendHoverLink: false,
      z: 1, // Behind main progress bar but above background
      radius: ['65.2%', '66%'], // Adjusted: Just outside the progress bar
      color: "#0A284B", // New: Subtle dark blue border
      label: {
        position: 'inner',
      },
      labelLine: {
        show: false,
      },
      tooltip: {
        show: false,
      },
      data: [ // Simplified data for a solid ring
        {
          value: 100,
          name: '',
        }
      ],
    },
    {
      // Inner thin border ring
      name: 'Inner Border',
      type: 'pie',
      legendHoverLink: false,
      z: 1, // Behind main progress bar but above background
      radius: ['49%', '49.8%'], // Adjusted: Between inner gradient and progress bar
      color: "#0A284B", // New: Subtle dark blue border
      label: {
        position: 'inner',
      },
      labelLine: {
        show: false,
      },
      tooltip: {
        show: false,
      },
      data: [ // Simplified data for a solid ring
        {
          value: 100,
          name: '',
        }
      ],
    },
  ],
}));
</script>

<template>
  <v-chart :option="option" autoresize />
</template>
