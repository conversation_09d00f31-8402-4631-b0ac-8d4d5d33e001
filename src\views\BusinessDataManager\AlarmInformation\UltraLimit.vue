<template>
  <AvTable
    :option="option"
    :searchForm="searchForm"
    :listApi="getOverLimitWarning"
  />
</template>

<script setup>
import AvTable from '/@/components/AvTable/index.vue'
import { getOverLimitWarning } from '/@/api/alarm/specialEvent'

// 搜索表单配置
const searchForm = {
  structureType: '',
  managementName: '',
  alarmLevel: '', // 修改为字符串类型，与AvTable的搜索表单保持一致
  alarmStatus: '',
  structureName: ''
}
const column = [
    {
      label: '报警级别',
      prop: 'alarmLevel',
      search: true,
      type: 'select',
      multiple: true,
      dicData: [
        { label: '一级超限', value: '1' },
        { label: '二级超限', value: '2' },
        { label: '三级超限', value: '3' }
      ],
      formatter: (row, column, cellValue) => {
        const levelMap = {
          '1': '一级超限',
          '2': '二级超限',
          '3': '三级超限'
        }
        return levelMap[cellValue] || cellValue[0]
      }
    },
    {
      label: '报警状态',
      prop: 'alarmStatus',
      search: true,
      type: 'select',
      dicData: [
        { label: '已处理', value: 'Handled' },
        { label: '处理中', value: 'Processing' },
        { label: '未处理', value: 'Unhandled' }
      ],
      formatter: (row, column, cellValue) => {
        const statusMap = {
          'Handled': '已处理',
          'Processing': '处理中',
          'Unhandled': '未处理'
        }
        return statusMap[cellValue] || cellValue
      }
    },
    {
      label: '报警开始时间',
      prop: 'alarmStartTime',
      formatter: (row, column, cellValue) => {
        if (cellValue) {
          return new Date(parseInt(cellValue)).toLocaleString()
        }
        return ''
      }
    },
    {
      label: '处理人',
      prop: 'handleUser'
    },
    {
      label: '处理人电话',
      prop: 'handleUserTel'
    },
    {
      label: '处理时间',
      prop: 'handleTime',
      formatter: (row, column, cellValue) => {
        if (cellValue) {
          return new Date(parseInt(cellValue)).toLocaleString()
        }
        return ''
      }
    },
    {
      label: '管养单位',
      prop: 'managementName',
      search: true
    },
    {
      label: '结构物名称',
      prop: 'structureName',
      search: true
    },
    {
      label: '结构物类型',
      prop: 'structureType',
      search: true,
      type: 'select',
      dicData: [
        { label: '桥梁', value: '1' },
        { label: '隧道', value: '2' },
        { label: '边坡', value: '3' },
        { label: '下穿通道', value: '4' }
      ],
      formatter: (row, column, cellValue) => {
        const typeMap = {
          '1': '桥梁',
          '2': '隧道',
          '3': '边坡',
          '4': '下穿通道'
        }
        return typeMap[cellValue] || cellValue
      }
    }
  ].map(i => ({ ...i, labelWidth: 120, searchLabelWidth: 100 }))

// 表格配置
const option = {
  border: true,
  stripe: true,
  menu: false, // 没有操作项
  addBtn: false,
  delBtn: false,
  editBtn: false,
  viewBtn: false,
  refreshBtn: true,
  index: true,
  columnBtn: false,
  header: false,
  align: 'center',
  searchMenuPosition: 'right',
  searchMenuSpan: 18,
  column
}


</script>