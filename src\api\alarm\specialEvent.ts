import request from '/@/utils/request';

/**
 * 获取特殊事件列表
 * @param params 查询参数
 */
export function getSpecialEvent(params?: any) {
  return request({
    url: '/hwm/alarm/getSpecialEvent',
    method: 'get',
    params: {
      size: 10,
      current: 1,
      ...params
    }
  });
}

/**
 * 获取超限预警列表
 * @param params 查询参数
 */
export function getOverLimitWarning(params?: any) {
  return request({
    url: '/hwm/alarm/getOverLimitWarning',
    method: 'get',
    params: {
      size: 10,
      current: 1,
      ...params
    }
  });
}
