import request from '/@/utils/request';

/**
 * 获取测点设备监测数据列表
 * @param params 查询参数
 * @returns Promise
 */
export const getDeviceMonitorData = (params: any) => {
  return request({
    url: '/hwm/deviceMonitor/getDeviceMonitorData',
    method: 'get',params
  });
};

/**
 * 获取测点设备监测详情
 * @param pointUniqueCode 测点唯一编码
 * @returns Promise
 */
export const getDeviceMonitorDetail = (pointUniqueCode: string) => {
  return request({
    url: '/hwm/deviceMonitor/getDeviceMonitorDetail',
    method: 'get',
    params: { pointUniqueCode }
  });
};
