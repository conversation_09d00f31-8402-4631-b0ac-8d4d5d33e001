// 配置工厂函数
// 用于生成标准化的表格和表单配置

import {
  managerOptions,
  roadLevelOptions,
  techStatusOptions
} from './common-options.js'

// 基础字段配置工厂函数
const createFieldConfig = (label, prop, options = {}) => {
  const defaultConfig = {
    label,
    prop,
    placeholder: `请输入${label}`,
    ...options
  }

  if (options.type === 'select' && options.dicData) {
    defaultConfig.placeholder = `请选择${label}`
  }

  return defaultConfig
}

// 搜索字段配置工厂函数
const createSearchFieldConfig = (label, prop, options = {}) => {
  return createFieldConfig(label, prop, {
    search: true,
    overHidden: true,
    ...options
  })
}

// 分组配置工厂函数
const createGroupConfig = (label, prop, options = {}) => {
  return {
    label,
    prop,
    labelWidth: 120,
    icon: 'el-icon-edit-outline',
    ...options
  }
}

/**
 * 创建表格配置
 * @param {Object} options 配置选项
 * @param {Array} options.searchColumns 搜索列配置
 * @param {Array} options.groups 分组配置
 * @returns {Object} 表格配置对象
 */
export const createTableConfig = ({ searchColumns = [], groups = [] }) => {
  return {
    column: searchColumns,
    group: groups
  }
}

/**
 * 创建基础数据分组配置
 * @param {Object} options 配置选项
 * @param {string} options.structureType 结构类型（如：桥梁、边坡）
 * @param {Array} options.additionalFields 额外字段配置
 * @param {Array} options.excludeFields 要排除的字段
 * @param {Object} options.customFields 自定义字段配置
 * @returns {Object} 基础数据分组配置
 */
export const createBaseDataGroup = ({ 
  structureType = '结构', 
  additionalFields = [], 
  excludeFields = [],
  customFields = {}
}) => {
  // 标准基础数据字段
  const standardFields = [
    createFieldConfig(`${structureType}名称`, 'structureName'),
    createFieldConfig(`${structureType}编号`, 'structureUniqueCode'),
    createFieldConfig('测点数量', 'monitorPoints'),
    createFieldConfig(`${structureType}位置`, 'structureLocation'),
    createFieldConfig('所在线路', 'locationRoad'),
    createFieldConfig('线路等级', 'locationType', {
      type: 'select',
      dicData: roadLevelOptions
    }),
    createFieldConfig('通车时间', 'openingDate'),
    createFieldConfig('中心桩号', 'centerStake'),
    createFieldConfig('起点桩号', 'startStake'),
    createFieldConfig('终点桩号', 'endStake'),
    createFieldConfig('经度', 'longitude'),
    createFieldConfig('纬度', 'latitude'),
    createFieldConfig('管养单位', 'managementName', {
      type: 'select',
      dicData: managerOptions
    }),
    createFieldConfig('管养联系人', 'managementUser'),
    createFieldConfig('管养联系电话', 'managementPhone'),
    createFieldConfig('运维单位', 'maintenanceName'),
    createFieldConfig('运维联系人', 'maintenanceUser'),
    createFieldConfig('运维联系电话', 'maintenancePhone')
  ]

  // 过滤排除的字段
  const filteredFields = standardFields.filter(field => 
    !excludeFields.includes(field.prop)
  )

  // 合并自定义字段
  const finalFields = filteredFields.map(field => {
    if (customFields[field.prop]) {
      return { ...field, ...customFields[field.prop] }
    }
    return field
  })

  // 添加额外字段
  finalFields.push(...additionalFields)

  return createGroupConfig('基础数据', 'base', {
    column: finalFields
  })
}

/**
 * 创建结构数据分组配置
 * @param {Object} options 配置选项
 * @param {Array} options.fields 字段配置数组
 * @param {number} options.labelWidth 标签宽度
 * @returns {Object} 结构数据分组配置
 */
export const createStructureDataGroup = ({ fields = [], labelWidth = 150 }) => {
  return createGroupConfig('结构数据', 'structure', {
    labelWidth,
    column: fields
  })
}

/**
 * 创建搜索表单初始值
 * @param {Array} searchFields 搜索字段配置
 * @param {Object} additionalFields 额外的初始值字段
 * @returns {Object} 搜索表单初始值对象
 */
export const createSearchForm = (searchFields = [], additionalFields = {}) => {
  const form = {}
  
  searchFields.forEach(field => {
    form[field.prop] = ''
  })
  
  return { ...form, ...additionalFields }
}

/**
 * 创建标准搜索列配置
 * @param {Object} options 配置选项
 * @param {string} options.structureType 结构类型
 * @param {Object} options.monitorScenariosConfig 监测场景配置
 * @param {Array} options.additionalColumns 额外的搜索列
 * @returns {Array} 搜索列配置数组
 */
export const createStandardSearchColumns = ({ 
  structureType = '结构',
  monitorScenariosConfig = {},
  additionalColumns = []
}) => {
  const standardColumns = [
    createSearchFieldConfig(`${structureType}名称`, 'structureName'),
    createSearchFieldConfig('管养单位', 'managementName', {
      type: 'select',
      dicData: managerOptions
    }),
    createSearchFieldConfig('路线等级', 'locationType', {
      type: 'select',
      dicData: roadLevelOptions
    }),
    createSearchFieldConfig('技术状况', 'technicalGrade', {
      type: 'select',
      dicData: techStatusOptions
    })
  ]

  // 添加监测场景配置
  if (monitorScenariosConfig.dicData) {
    standardColumns.push({
      label: '监测场景',
      prop: 'monitorScenarios',
      type: 'select',
      dicData: monitorScenariosConfig.dicData,
      search: true,
      placeholder: '请选择监测场景',
      overHidden: true
    })
  }

  return [...standardColumns, ...additionalColumns]
}

/**
 * 创建完整的结构配置
 * @param {Object} options 配置选项
 * @returns {Object} 完整的配置对象，包含表格配置和搜索表单
 */
export const createStructureConfig = (options) => {
  const {
    structureType,
    monitorScenariosConfig,
    baseDataOptions = {},
    structureDataFields = [],
    additionalSearchColumns = [],
    additionalSearchFormFields = {}
  } = options

  // 创建搜索列
  const searchColumns = createStandardSearchColumns({
    structureType,
    monitorScenariosConfig,
    additionalColumns: additionalSearchColumns
  })

  // 创建基础数据分组
  const baseDataGroup = createBaseDataGroup({
    structureType,
    ...baseDataOptions
  })

  // 创建结构数据分组
  const structureDataGroup = createStructureDataGroup({
    fields: structureDataFields
  })

  // 创建表格配置
  const tableConfig = createTableConfig({
    searchColumns,
    groups: [baseDataGroup, structureDataGroup]
  })

  // 创建搜索表单
  const searchForm = createSearchForm(searchColumns, additionalSearchFormFields)

  return {
    tableConfig,
    searchForm
  }
}
