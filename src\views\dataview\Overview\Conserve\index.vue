<script setup>
import chart from './chart.vue';
import { ref, computed, watch } from 'vue';
import {useRoadFilterApi } from '/@/composables/useApi';
import { getBridgeAndTunnelMaintenanceOverview } from '/@/api/dataview/base';

// 使用 Composable 获取数据，并传入 API 函数
const { data: apiData, isLoading, error } =useRoadFilterApi(getBridgeAndTunnelMaintenanceOverview);

// 将从API获取的数据处理成模板和chart组件所需的数据结构
const processedData = computed(() => {
  if (!apiData.value || isLoading.value || error.value) {
    // 如果数据未加载、正在加载或发生错误，可以返回空数组或默认值
    // 这里返回空数组，或者你可以根据需要填充一些表示加载中的占位符数据
    return []; 
  }

  const raw = apiData.value;
  const result = [
    {
      type: '桥梁',
      checkType: '经常性检查巡查率',
      percentage: raw.bridgeInspectionRate,
    },
    {
      type: '隧道',
      checkType: '经常性检查巡查率',
      percentage: raw.tunnelInspectionRate,
    },
    {
      type: '桥梁',
      checkType: '定期检查覆盖率',
      percentage: raw.bridgeCoverageRate,
    },
    {
      type: '隧道',
      checkType: '定期检查覆盖率',
      percentage: raw.tunnelCoverageRate,
    },
  ];

  return result.map(item => {
    let color = '';
    // 根据类型和检查类型分配颜色
    if (item.type === '桥梁' && item.checkType === '经常性检查巡查率') color = '#F6DE79';
    else if (item.type === '隧道' && item.checkType === '经常性检查巡查率') color = '#2DCBE5';
    else if (item.type === '桥梁' && item.checkType === '定期检查覆盖率') color = '#F9BF8B';
    else if (item.type === '隧道' && item.checkType === '定期检查覆盖率') color = '#31D9FF';

    return {
      displayName: item.type, // 用于div中显示的名称 (桥梁/隧道)
      label: item.checkType, // 用于div中显示的标签 (巡查率/覆盖率)
      color: color, // 颜色
      valueString: `${item.percentage.toFixed(2)}%`, // 传递给chart的value，保留两位小数
      chartSubText: item.checkType, // 传递给chart的name，作为副标题
    };
  });
});


</script>

<template>
  <div class="w-full h-52 flex flex-wrap">
    <div v-if="isLoading" class="w-full h-full flex justify-center items-center">
      <!-- 你可以在这里放置一个加载指示器 -->
      <span>加载数据中...</span>
    </div>
    <div v-else-if="error" class="w-full h-full flex justify-center items-center text-red-500">
      <!-- 你可以在这里显示错误信息 -->
      <span>加载数据失败: {{ error }}</span>
    </div>
    <template v-else>
      <div class="w-1/2 h-1/2 flex" v-for="(item, index) in processedData" :key="index">
        <div class="w-[45%] h-full">
          <chart :color="item.color" :data="{ name: item.chartSubText, value: item.valueString }" />
        </div>
        <div class="w-[55%] h-full flex flex-col justify-center ">
          <span :style="{ color: item.color }">{{ item.displayName }}</span>
          <span class="text-sm">{{ item.label }}</span>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped></style>