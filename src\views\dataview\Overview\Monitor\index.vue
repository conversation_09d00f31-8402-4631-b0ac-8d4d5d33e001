<script setup>
// @ts-nocheck
import { computed } from 'vue'
import Corner from '../../Corner/index.vue'
import Chart from './chart.vue'
import Chart1 from './chart1.vue'
import {getMonitorOverview} from '/@/api/dataview/base'
import {useRoadFilterApi } from '/@/composables/useApi';
const { data: apiData } =useRoadFilterApi(getMonitorOverview);
const online = computed(() => ({
  online: { label: '基本完好', count: parseInt(apiData.value?.基本完好 || '1'), color: '#1DCDB0' },
  offline: { label: '轻微离线', count: parseInt(apiData.value?.轻微离线 || '0'), color: '#ECCB3A' },
  error: { label: '严重离线', count: parseInt(apiData.value?.严重离线 || '1'), color: '#BC2140' }
}))

const monit = computed(() => [
  { label: '总设备', count: parseInt(apiData.value?.设备总数 || '2'), color: '#19c' },
  { label: '在线设备', count: parseInt(apiData.value?.在线设备数 || '1'), color: '#17A592' },
  { label: '离线设备', count: parseInt(apiData.value?.离线设备数 || '1'), color: '#E1C23A' }
])

const count = computed(() => parseInt(apiData.value?.设备类型数 || '2'))




const onlineRate = computed(() => {
  const total = parseInt(apiData.value?.设备总数 || '1')
  const online = parseInt(apiData.value?.在线设备数 || '0')
  return total > 0 ? (online / total) * 100 : 0
})

const intactRate = computed(() => {
  // 完好率
  return  +apiData.value?.完好率 *100 || 0
  
})

const getFontSize = (count) => {
  const len = count.toString().length;
  if (len > 6) return '18px';
  if (len > 4) return '22px';
  return '26px';
};

const getTypeCountFontSize = (count) => {
  const len = count.toString().length;
  if (len > 8) return '12px';
  if (len > 6) return '13px';
  if (len > 4) return '14px';
  return '15px';
};
</script>

<template>
  <div class="w-full h-60 flex flex-col justify-around items-center">
    <div class="w-full h-28 relative flex justify-center items-center">
      <div class="w-[95%] h-[95%] border-2 border-[#0B2B7A] flex ">
        <div v-for="(item, key) in online" :key="key" class="w-[24%] h-full flex  flex-col justify-center items-center">
          <span class="text-sm">{{ item.label }}</span>
          <span class="whitespace-nowrap" :style="{ color: item.color, fontSize: getFontSize(item.count) }">{{ item.count }}</span>
        </div>
        <div class="flex-1 flex flex-col justify-center items-center">
          <Chart :intactRate class="w-full h-[70%]" />
          <span class="text-gray-400">完好率</span>
        </div>
      </div>
      <Corner />
    </div>
    <div class="w-full h-28 relative flex justify-center items-center">
      <div class="w-[95%] h-[95%] border-2 border-[#0B2B7A] flex gap-1">
        <div class="w-1/4 h-full">
          <Chart1 :onlineRate class="w-full h-full" />
        </div>
        <div class="flex-1 ">
          <div class="w-full h-1/2 flex flex-col justify-evenly p-1">
            <div class="flex items-center justify-around">
              <span class="text-[#9AB1D3]">{{ monit[0].label }}:</span>
              <span class="ml-2 whitespace-nowrap" :style="{ color: monit[0].color, fontSize: getTypeCountFontSize(monit[0].count) }">{{ monit[0].count }}</span>
                <span class="text-[#9AB1D3]">{{ monit[1].label }}:</span>
                <span class="ml-2 whitespace-nowrap" :style="{ color: monit[1].color, fontSize: getTypeCountFontSize(monit[1].count) }">{{ monit[1].count }}</span>
                <span class="text-[#9AB1D3]">{{ monit[2].label }}:</span>
                <span class="ml-2 whitespace-nowrap" :style="{ color: monit[2].color, fontSize: getTypeCountFontSize(monit[2].count) }">{{ monit[2].count }}</span>
            </div>
          </div>
          <div class="w-full h-1/2 bg border border-gray-500 bg-[#cccccc11] flex justify-between p-2 items-center">
            <span>监测类型数</span>
            <span class="whitespace-nowrap" :style="{ fontSize: '24px'}">{{ count }}</span>
          </div>
        </div>
      </div>
      <Corner />
    </div>
  </div>
</template>

<style scoped>
.bg {
  background-image: url('./Mask.png');
  background-size: cover;
}
</style>