<script setup>
import { ref, computed } from 'vue' // 引入 computed
import VChart from 'vue-echarts'
import * as echarts from 'echarts'

// 1. 修改 props 定义以匹配你的新数据结构
const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({ names: [], values: [] }) // 提供一个默认值防止报错
  }
})

// 2. 使用 computed 创建响应式的 option
// 当 props.data 变化时，option 会自动重新计算
const option = computed(() => {
  return {
    backgroundColor: '#0A1A3C', // 保留所有你喜欢的样式
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      textStyle: {
        color: '#FFFFFF'
      },
      backgroundColor: 'rgba(0,0,0,0.8)',
      borderColor: '#333333'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '5%',
      top: '10%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        // 3. 将静态数据替换为 props.data.names
        data: props.data.names,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5B6A8A'
          }
        },
        axisTick: {
          show: true,
          alignWithLabel: true,
          lineStyle: {
            color: '#5B6A8A'
          }
        },
        axisLabel: {
          color: '#FFFFFF',
          fontSize: 14
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 25,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#5B6A8A'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#FFFFFF',
          fontSize: 14,
          margin: 8
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#3A4B6C',
            type: 'dashed'
          }
        }
      }
    ],
    series: [
      {
        name: '在线率', // 你可以根据需要改成 '在线率' 或其他
        type: 'bar',
        barWidth: '30%',
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(0, 0, 0, 0.25)'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#63D6F9' },
              { offset: 1, color: '#1E6FFF' }
            ]
          )
        },
        // 4. 将静态数据替换为 props.data.values
        data: props.data.values
      }
    ]
  }
})
</script>

<template>
  <!-- 5. 使用 v-chart 组件，它会自动处理响应式更新 -->
  <v-chart class="w-full h-full" :option="option" autoresize />
</template>

<style scoped>
/* 如果需要可以添加样式 */
</style>