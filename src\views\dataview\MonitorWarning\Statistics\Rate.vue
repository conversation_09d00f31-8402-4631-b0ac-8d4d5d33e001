<script setup>
import { computed } from 'vue' // 1. 导入 computed
import VChart from 'vue-echarts'

// 接受props 比例和名称
const props = defineProps({
  rate: {
    type: Number,
    required: true
  },
  name: {
    type: String,
    required: true
  }
})

// 2. 使用 computed 替代 ref
const option = computed(() => {
  // computed 的 getter 函数会在 props.rate 或 props.name 变化时重新执行
  return {
    series: [
      {
        type: 'gauge',
        startAngle: 225,
        endAngle: -45,
        radius: '100%',
        pointer: { show: false },
        progress: {
          show: true,
          roundCap: true,
          width: 5,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#FFD700' },
                { offset: 1, color: '#082752' }
              ]
            }
          }
        },
        axisLine: {
          lineStyle: {
            width: 5,
            color: [
              [0.85, '#082752'],
              [1, 'rgba(0,0,0,0)']
            ]
          }
        },
        splitLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show:false },
        detail: {
          valueAnimation: true,
          fontSize: 14,
          color: '#00CFFF',
          offsetCenter: [0, '-10%'],
          formatter: '{value}%'
        },
        title: {
          offsetCenter: [0, '30%'],
          fontSize: 10,
          color: '#fff'
        },
        // 3. 在这里使用 props 的最新值
        data: [{ value: props.rate , name: props.name }]
      }
    ]
  }
})

</script>

<template>
  <!-- 注意：v-chart 的 option 绑定是 :option="option" -->
  <v-chart :option="option" autoresize />
</template>