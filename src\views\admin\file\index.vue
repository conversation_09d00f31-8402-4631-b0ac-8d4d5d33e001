<template>
	<div class="layout-padding">
		<div class="material-index">
			<el-card class="!border-none" shadow="never">
				<el-tabs v-model="activeTab">
					<el-tab-pane v-for="item in tabsMap" :label="item.name" :name="item.type" :index="item.type" :key="item.type" lazy>
						<material :type="item.type" mode="page" file-size="120px" :limit="-1" :page-size="20" />
					</el-tab-pane>
				</el-tabs>
			</el-card>
		</div>
	</div>
</template>

<script lang="ts" setup name="fileCenter">
import { useI18n } from 'vue-i18n';

const Material = defineAsyncComponent(() => import('/@/components/Material/index.vue'));
const { t } = useI18n();
const tabsMap = [
	{
		type: 'image',
		name: t('file.image'),
	},
	{
		type: 'video',
		name: t('file.video'),
	},
	{
		type: 'file',
		name: t('file.file'),
	},
];
const activeTab = ref('image');
</script>

<style lang="scss" scoped>
.material-index {
	min-width: 700px;
	:deep(.el-tabs) {
		height: calc(100vh - 180px);
		display: flex;
		flex-direction: column;
		.el-tabs__header {
			margin-bottom: 0 !important;
		}
		.el-tabs__content,
		.el-tab-pane {
			min-height: 0;
			flex: 1;
			display: flex;
			flex-direction: column;
		}
	}
}
</style>
