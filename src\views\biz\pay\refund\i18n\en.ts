export default {
	refund: {
		index: '#',
		importpayRefundOrderTip: 'import PayRefundOrder',
		refundOrderId: 'refundOrderId',
		payOrderId: 'payOrderId',
		channelPayOrderNo: 'channelPayOrderNo',
		mchId: 'mchId',
		mchRefundNo: 'mchRefundNo',
		channelId: 'channelId',
		payAmount: 'payAmount',
		refundAmount: 'refundAmount',
		currency: 'currency',
		status: 'status',
		result: 'result',
		clientIp: 'clientIp',
		device: 'device',
		remark: 'remark',
		channelUser: 'channelUser',
		username: 'username',
		channelMchId: 'channelMchId',
		channelOrderNo: 'channelOrderNo',
		channelErrCode: 'channelErrCode',
		channelErrMsg: 'channelErrMsg',
		extra: 'extra',
		notifyUrl: 'notifyUrl',
		param1: 'param1',
		param2: 'param2',
		expireTime: 'expireTime',
		refundSuccTime: 'refundSuccTime',
		delFlag: 'delFlag',
		createTime: 'createTime',
		updateTime: 'updateTime',
		tenantId: 'tenantId',
		inputRefundOrderIdTip: 'input refundOrderId',
		inputPayOrderIdTip: 'input payOrderId',
		inputChannelPayOrderNoTip: 'input channelPayOrderNo',
		inputMchIdTip: 'input mchId',
		inputMchRefundNoTip: 'input mchRefundNo',
		inputChannelIdTip: 'input channelId',
		inputPayAmountTip: 'input payAmount',
		inputRefundAmountTip: 'input refundAmount',
		inputCurrencyTip: 'input currency',
		inputStatusTip: 'input status',
		inputResultTip: 'input result',
		inputClientIpTip: 'input clientIp',
		inputDeviceTip: 'input device',
		inputRemarkTip: 'input remark',
		inputChannelUserTip: 'input channelUser',
		inputUsernameTip: 'input username',
		inputChannelMchIdTip: 'input channelMchId',
		inputChannelOrderNoTip: 'input channelOrderNo',
		inputChannelErrCodeTip: 'input channelErrCode',
		inputChannelErrMsgTip: 'input channelErrMsg',
		inputExtraTip: 'input extra',
		inputNotifyUrlTip: 'input notifyUrl',
		inputParam1Tip: 'input param1',
		inputParam2Tip: 'input param2',
		inputExpireTimeTip: 'input expireTime',
		inputRefundSuccTimeTip: 'input refundSuccTime',
		inputDelFlagTip: 'input delFlag',
		inputCreateTimeTip: 'input createTime',
		inputUpdateTimeTip: 'input updateTime',
		inputTenantIdTip: 'input tenantId',
	},
};
