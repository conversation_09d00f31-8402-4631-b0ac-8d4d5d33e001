<template>
  <div class="flex justify-around items-center w-[23%] h-1/2 top-7 left-12  absolute z-30">
    <div class="w-[29%] h-[80%] flex justify-around items-center relative cursor-pointer" @click="pageChange(key)" v-for="(value, key) in pages">
      <img class="absolute w-full h-full" :src="curPage===key?'/image/Frame 427321878.png':'/image/Frame 427321879.png'">
      <span v-if="curPage!==key" class="text-2xl text-[#64AEF7]">
        {{ value }}
      </span>
      <span v-else class="text-2xl text-white">
        {{ value }}
      </span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { dataview} from '/@/stores/dataview'
const {curPage} = storeToRefs(dataview())
const pages = {
  Overview: '总览',
  MonitorWarning:'监测预警',
  Structure:'单结构物'
}
const pageChange = (page: string) => {
  curPage.value = page
}
</script>
<style scoped>
</style>
