// 测点设备管理配置

// 管养单位选项
const managementOptions = [
  { label: '宁波大通开发有限公司', value: '宁波大通开发有限公司' },
  { label: '江北区公路与运输管理中心', value: '江北区公路与运输管理中心' },
  { label: '其他单位', value: '其他单位' }
]

// 运维单位选项
const maintenanceOptions = [
  { label: '宁波市交通建设工程试验检测中心有限公司', value: '宁波市交通建设工程试验检测中心有限公司' },
  { label: '其他运维单位', value: '其他运维单位' }
]

// 监测类别选项
const monitorCategoryOptions = [
  { label: 'RES', value: 'RES' },
  { label: 'ENV', value: 'ENV' },
  { label: 'STR', value: 'STR' }
]

// 监测类型选项
const monitorTypeOptions = [
  { label: 'VIC', value: 'VIC' },
  { label: 'ACC', value: 'ACC' },
  { label: 'DIS', value: 'DIS' },
  { label: 'STR', value: 'STR' }
]

// 设备状态选项
const deviceStatusOptions = [
  { label: '正常', value: 1 },
  { label: '异常', value: 0 },
  { label: '离线', value: 2 }
]
const showFiveFont = 100
// 表格列配置（包含搜索和显示）
const searchColumns = [
  {
    label: '结构物名称',
    prop: 'structureName',
    search: true,
    overHidden: true,
    placeholder: '请输入结构物名称',
    searchLabelWidth: showFiveFont,
  },
  {
    label: '管养单位',
    prop: 'managementName',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: managementOptions,
    placeholder: '请选择管养单位'
  },
  {
    label: '运维单位',
    prop: 'maintenanceName',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: maintenanceOptions,
    placeholder: '请选择运维单位'
  },
  {
    label: '测点位置',
    prop: 'pointPosition',
    overHidden: true
  },
  {
    label: '监测类别',
    prop: 'monitorCategory',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: monitorCategoryOptions,
    placeholder: '请选择监测类别'
  },
  {
    label: '监测类型',
    prop: 'monitorTypeCode',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: monitorTypeOptions,
    placeholder: '请选择监测类型',
    searchLabelWidth: showFiveFont,
  },
  {
    label: '设备状态',
    prop: 'status',
    search: true,
    overHidden: true,
    type: 'select',
    dicData: deviceStatusOptions,
    placeholder: '请选择设备状态'
  }
].map(i => ({ ...i, labelWidth: showFiveFont }))

// 测点信息分组配置
const pointInfoGroup = {
  label: '测点信息',
  prop: 'pointInfo',
  labelWidth: 100,
  icon: 'el-icon-location-outline',
  column: [
    { label: '结构物名称', prop: 'structureName', placeholder: '请输入结构物名称' },
    { label: '结构物编号', prop: 'structureUniqueCode', placeholder: '请输入结构物编号' },
    {
      label: '管养单位',
      prop: 'managementName',
      type: 'select',
      dicData: managementOptions,
      placeholder: '请选择管养单位'
    },
    {
      label: '运维单位',
      prop: 'maintenanceName',
      type: 'select',
      dicData: maintenanceOptions,
      placeholder: '请选择运维单位'
    },
    { label: '测点位置', prop: 'pointPosition', placeholder: '请输入测点位置' },
    { label: '测点编号', prop: 'pointUniqueCode', placeholder: '请输入测点编号' },
    {
      label: '监测类别',
      prop: 'monitorCategory',
      type: 'select',
      dicData: monitorCategoryOptions,
      placeholder: '请选择监测类别'
    },
    {
      label: '监测类型',
      prop: 'monitorTypeCode',
      type: 'select',
      dicData: monitorTypeOptions,
      placeholder: '请选择监测类型'
    },
    { label: '监测内容', prop: 'monitorContent', placeholder: '请输入监测内容' }
  ]
}

// 设备信息分组配置
const deviceInfoGroup = {
  label: '设备信息',
  prop: 'deviceInfo',
  labelWidth: 120,
  icon: 'el-icon-cpu',
  column: [
    { label: '设备名称', prop: 'deviceName', placeholder: '请输入设备名称' },
    { label: '设备编码', prop: 'deviceUniqueCode', placeholder: '请输入设备编码' },
    { label: '设备类型', prop: 'deviceType', placeholder: '请输入设备类型' },
    {
      label: '设备状态',
      prop: 'status',
      type: 'select',
      dicData: deviceStatusOptions,
      placeholder: '请选择设备状态'
    },
    { label: '设备品牌', prop: 'sensorBrand', placeholder: '请输入设备品牌' },
    { label: '设备型号', prop: 'sensorModel', placeholder: '请输入设备型号' },
    { label: '采样频率', prop: 'samplingFrequence', placeholder: '请输入采样频率' },
    { label: '初始值', prop: 'initValue', placeholder: '请输入初始值' },
    { label: '一级预警上限', prop: 'levelOneUpLimit', placeholder: '请输入一级预警上限' },
    { label: '一级预警下限', prop: 'levelOneDownLimit', placeholder: '请输入一级预警下限' },
    { label: '二级预警上限', prop: 'levelTwoUpLimit', placeholder: '请输入二级预警上限' },
    { label: '二级预警下限', prop: 'levelTwoDownLimit', placeholder: '请输入二级预警下限' },
    { label: '三级预警上限', prop: 'levelThreeUpLimit', placeholder: '请输入三级预警上限' },
    { label: '三级预警下限', prop: 'levelThreeDownLimit', placeholder: '请输入三级预警下限' },
    {
      label: '安装日期',
      prop: 'installDate',
      type: 'date',
      format: 'YYYY-MM-DD',
      placeholder: '请选择安装日期'
    }
  ]
}

// 表格配置
export const pointOption = {
  column: searchColumns,
  group: [pointInfoGroup, deviceInfoGroup],
  editBtn: false,  // 根据用户说明，这个表格没有编辑功能
  viewBtn: true,   // 只有查看功能
  addBtn: false,
  delBtn: false
}

// 搜索表单初始值
export const pointSearchForm = {
  structureName: '',
  managementName: '',
  maintenanceName: '',
  monitorCategory: '',
  monitorTypeCode: '',
  status: ''
}

// 导出选项数据供其他地方使用
export {
  managementOptions,
  maintenanceOptions,
  monitorCategoryOptions,
  monitorTypeOptions,
  deviceStatusOptions
}
