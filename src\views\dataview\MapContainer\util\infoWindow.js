// @ts-nocheck
import { ref } from 'vue';

// Helper function to create the HTML content for the info window
const createInfoWindowContent = (info) => {
    const healthTextMap = {
        0: '基本完好',
        1: '轻微异常',
        2: '中等异常',
        3: '严重异常',
    };
    const healthLevel = info.value.healthDegree;
    const healthText = healthTextMap[healthLevel] || '未知';

    const healthColorMap = {
        0: 'text-cyan-300', // Blue/Cyan for normal
        1: 'text-yellow-400', // Yellow for slight
        2: 'text-orange-400', // Orange for moderate
        3: 'text-red-500', // Red for severe
    };
    const healthColorClass = healthColorMap[healthLevel] || 'text-gray-400'; // Gray for unknown

    const points = Number(info.value.monitorPoints) || 0;
    const onlineRate = Number(info.value.deviceOnlineRate) || 0;
    const offlineCount = Math.round(points * (1 - onlineRate));

    const list = info.value.deviceList || {};

    // 分类映射 - 本地定义避免循环依赖
    const CATEGORY_NAMES = {
        environment: '环境类',
        response: '结构响应类',
        change: '结构变化类',
        action: '作用类',
        L1: '变形监测',
        L2: '物理场监测',
        L3: '影响因素监测',
        L4: '宏观现象监测',
        SDJD: '隧道机电监测',
    };

    // 动态生成监测统计，只显示实际存在的类别
    const monitorStats = {};
    Object.keys(list).forEach(key => {
        if (CATEGORY_NAMES[key] && Array.isArray(list[key]) ) {
            monitorStats[key] = {
                name: CATEGORY_NAMES[key],
                count: list[key].length
            };
        }
    });

    return `
        <div class="structure-card-amap w-[400px] bg-gradient-to-br from-[#0a1446] to-[#0f1a56] text-white rounded-lg shadow-2xl p-4 z-50 border border-cyan-400/30 font-sans">
    
            <!-- Header -->
            <div class="flex justify-between items-center mb-4 border-b border-cyan-400/20 pb-2">
                <h3 class="text-xl font-bold text-cyan-300">${info.value.structureName || '加载中...'}</h3>
                <div class="flex items-center space-x-4">
									<button id="details-btn" class="text-sm text-cyan-300 hover:underline">详情</button>
									<button id="info-window-close-btn" class="text-cyan-300 hover:text-white transition-colors text-2xl">&times;</button>
								</div>
            </div>

            <!-- Main Content Grid -->
                <img id="structure-image" src="${info.value.imageUrl}" class="w-full h-40 object-cover rounded-lg mb-2 cursor-pointer hover:opacity-80 transition-opacity">
            <div class="grid grid-cols-3 gap-x-6 gap-y-1 text-sm">
    <!-- 第一行: 主要信息 -->
    <div>
        <span class="text-cyan-400/80 block">健康度</span>
        <span class="text-lg font-semibold ${healthColorClass}">${healthText}</span>
    </div>
    <div>
        <span class="text-cyan-400/80 block">技术等级</span>
        <span class="text-base">${info.value.technicalGrade||'-'}</span>
    </div>
    <div>
        <span class="text-cyan-400/80 block">结构类型</span>
        <span class="text-base">${info.value.structureStyle||'-'}</span>
    </div>

    <!-- 第二行: 三个带数字的统计项 -->
    <div>
        <span class="text-cyan-400/80 block">预警数</span>
        <span class="font-mono text-lg text-yellow-400">${info.value.warningCount || 0}</span>
    </div>
    <div>
        <span class="text-cyan-400/80 block">离线数</span>
        <span class="font-mono text-lg text-red-500">${offlineCount}</span>
    </div>
    <div>
        <span class="text-cyan-400/80 block">未处置数</span>
        <span class="font-mono text-lg text-orange-400">${info.value.undisposedCount || 0}</span>
    </div>

    <!-- 第三行: 管养单位跨两列，剩余项补充 -->
    <div class="col-span-2"> <!-- 使用 col-span-2 使此元素占据两列 -->
        <span class="text-cyan-400/80 block">管养单位</span>
        <span class="text-base">${info.value.managementAlias}</span>
    </div>
    <div>
        <span class="text-cyan-400/80 block">监测点位</span>
        <span class="font-mono text-lg">${info.value.monitorPoints || 0}</span>
    </div>
</div>
            <!-- Divider -->
            <div class="my-2 border-t border-cyan-400/20"></div>

            <!-- Monitoring Stats -->
            <div>
                <h4 class="text-cyan-300 mb-2 text-base font-semibold">监测类型统计</h4>
                <ul class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                    ${Object.values(monitorStats).map(stat =>
                        `<li class="flex justify-between"><span>${stat.name}:</span> <span class="font-mono text-cyan-300">${stat.count}</span></li>`
                    ).join('')}
                </ul>
            </div>
        </div>`;
};

// Main function to create and manage the info window
export const createStructureInfoWindow = (AMap, map, position, info, curPage) => {
    const content = createInfoWindowContent(info);

    const infoWindow = new AMap.InfoWindow({
        isCustom: true,
        content: content,
        offset: new AMap.Pixel(0, -40),
        closeWhenClick: true
    });

    infoWindow.on('open', () => {
        const closeBtn = document.getElementById('info-window-close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                infoWindow.close();
            });
        }
        const detailsBtn = document.getElementById('details-btn');
        if (detailsBtn) {
            detailsBtn.addEventListener('click', () => {
                // 导入 store 来设置结构物代码
                import('/@/stores/dataview').then(({ dataview }) => {
                    const store = dataview();
                    // 设置当前结构物代码，确保单结构物页面显示正确的数据
                    store.structureCode = info.value.structureUniqueCode;
                    // 切换到单结构物页面
                    curPage.value = 'Structure';
                    infoWindow.close();
                });
            });
        }

        // 给图片添加点击事件，功能与详情按钮相同
        const structureImage = document.getElementById('structure-image');
        if (structureImage) {
            structureImage.addEventListener('click', () => {
                // 导入 store 来设置结构物代码
                import('/@/stores/dataview').then(({ dataview }) => {
                    const store = dataview();
                    // 设置当前结构物代码，确保单结构物页面显示正确的数据
                    store.structureCode = info.value.structureUniqueCode;
                    // 切换到单结构物页面
                    curPage.value = 'Structure';
                    infoWindow.close();
                });
            });
        }
    });

    infoWindow.open(map, position);
    return infoWindow;
}; 