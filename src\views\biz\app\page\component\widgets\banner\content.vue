<template>
	<div class="banner" :style="styles">
		<div class="banner-image w-full h-full">
			<decoration-img width="100%" :height="styles.height || height" :src="getImage" fit="contain" />
		</div>
	</div>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue';
import type options from './options';
import DecorationImg from '../../decoration-img.vue';
type OptionsType = ReturnType<typeof options>;
const props = defineProps({
	content: {
		type: Object as PropType<OptionsType['content']>,
		default: () => ({}),
	},
	styles: {
		type: Object as PropType<OptionsType['styles']>,
		default: () => ({}),
	},
	height: {
		type: String,
		default: '170px',
	},
});

const getImage = computed(() => {
	const { data } = props.content;
	if (Array.isArray(data)) {
		return data[0] ? data[0].image : '';
	}
	return '';
});
</script>

<style lang="scss" scoped></style>
