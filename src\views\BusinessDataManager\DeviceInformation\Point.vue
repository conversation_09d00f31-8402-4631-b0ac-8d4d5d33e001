<template>
  <AvTable
    :option="pointOption"
    :searchForm="pointSearchForm"
    :listApi="getDeviceMonitorData"
    :detailApi="customDetailApi"
    :customSearch="handleCustomSearch"
    @search="handleSearch"
    @load="handleLoad"
  />
</template>

<script setup>
import AvTable from '../AvTable.vue'
import { pointOption, pointSearchForm } from './configs/index.js'
import { getDeviceMonitorData, getDeviceMonitorDetail } from '/@/api/dataview/device-monitor'

// 自定义详情API - 适配AvTable组件的调用方式
function customDetailApi(formData) {
  // AvTable会传入form.value，我们需要从中获取pointUniqueCode
  const pointUniqueCode = formData?.pointUniqueCode || formData?.structureUniqueCode
  if (!pointUniqueCode) {
    return Promise.reject(new Error('缺少pointUniqueCode参数'))
  }
  return getDeviceMonitorDetail(pointUniqueCode)
}

// 自定义搜索处理
function handleCustomSearch(params, done, updateTableData) {
  console.log('测点设备搜索参数:', params)

  getDeviceMonitorData(params).then(res => {
    updateTableData(res)
    done()
  }).catch(err => {
    console.error('搜索失败:', err)
    done()
  })
}

// 搜索事件处理
function handleSearch(params) {
  console.log('搜索事件:', params)
}

// 加载事件处理
function handleLoad() {
  console.log('加载事件')
}
</script>
