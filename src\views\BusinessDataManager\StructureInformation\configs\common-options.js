// 共享选项数据配置
// 用于在多个配置文件中重复使用的选项数据

// 管养单位选项
export const managerOptions = [
  { label: '大通公司', value: '大通公司' },
  { label: '江北区公路与运输管理中心', value: '江北区公路与运输管理中心' },
  { label: '其他单位', value: '其他单位' }
]

// 路线等级选项
export const roadLevelOptions = [
  { label: '高速', value: 1 },
  { label: '国道', value: 2 },
  { label: '农村道路', value: 3 }
]

// 技术状况选项
export const techStatusOptions = [
  { label: '1类', value: '1类' },
  { label: '2类', value: '2类' },
  { label: '3类', value: '3类' },
  { label: '4类', value: '4类' },
  { label: '5类', value: '5类' }
]

// 养护等级选项
export const designGradeOptions = [
  { label: 'Ⅰ级', value: 'Ⅰ级' },
  { label: 'Ⅱ级', value: 'Ⅱ级' },
  { label: 'Ⅲ级', value: 'Ⅲ级' },
  { label: 'Ⅳ级', value: 'Ⅳ级' },
  { label: 'Ⅴ级', value: 'Ⅴ级' }
]

// 跨越地物类型选项
export const crossingObjectOptions = [
  { label: '河流', value: '河流' },
  { label: '道路', value: '道路' },
  { label: '铁路', value: '铁路' },
  { label: '沟谷', value: '沟谷' },
  { label: '其他', value: '其他' }
]

// 桥梁监测场景选项
export const bridgeSceneOptions = [
  { label: '桥梁标准化监测', value: '桥梁标准化监测' },
  { label: '长大桥梁监测', value: '长大桥梁监测' },
  { label: '结构风险高桥梁', value: '结构风险高桥梁' },
  { label: '汇流急流区桥梁', value: '汇流急流区桥梁' },
  { label: '饱和交通桥梁', value: '饱和交通桥梁' },
  { label: '重载交通桥梁', value: '重载交通桥梁' },
  { label: '隧道结构监测', value: '隧道结构监测' },
  { label: '隧道机电监测', value: '隧道机电监测' },
  { label: '边坡专业监测', value: '边坡专业监测' },
  { label: '边坡事件监测', value: '边坡事件监测' },
  { label: '下穿通道监测', value: '下穿通道监测' }
]

// 隧道监测场景选项
export const tunnelSceneOptions = [
  { label: '隧道结构监测', value: '隧道结构监测' },
  { label: '隧道机电监测', value: '隧道机电监测' },
  { label: '隧道环境监测', value: '隧道环境监测' },
  { label: '隧道安全监测', value: '隧道安全监测' }
]

// 边坡监测场景选项
export const slopeSceneOptions = [
  { label: '边坡专业监测', value: '边坡专业监测' },
  { label: '边坡事件监测', value: '边坡事件监测' }
]

// 边坡类型选项
export const slopeTypeOptions = [
  { label: '土质边坡', value: '土质边坡' },
  { label: '岩质边坡', value: '岩质边坡' },
  { label: '土岩混合边坡', value: '土岩混合边坡' },
  { label: '填方边坡', value: '填方边坡' },
  { label: '挖方边坡', value: '挖方边坡' }
]

// 风险等级选项
export const riskLevelOptions = [
  { label: '低风险', value: '低风险' },
  { label: '中风险', value: '中风险' },
  { label: '高风险', value: '高风险' },
  { label: '极高风险', value: '极高风险' }
]

// 支护方式选项
export const supportMethodOptions = [
  { label: '重力式挡土墙', value: '重力式挡土墙' },
  { label: '悬臂式挡土墙', value: '悬臂式挡土墙' },
  { label: '扶壁式挡土墙', value: '扶壁式挡土墙' },
  { label: '锚杆支护', value: '锚杆支护' },
  { label: '锚索支护', value: '锚索支护' },
  { label: '喷锚支护', value: '喷锚支护' },
  { label: '格构梁', value: '格构梁' },
  { label: '护面墙', value: '护面墙' },
  { label: '植被防护', value: '植被防护' },
  { label: '综合支护', value: '综合支护' }
]
