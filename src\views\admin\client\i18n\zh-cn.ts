export default {
	client: {
		index: '#',
		importsysOauthClientDetailsTip: '导入终端信息表',
		id: 'ID',
		clientId: '客户端id',
		resourceIds: '',
		clientSecret: '密钥',
		scope: '域',
		authorizedGrantTypes: '授权模式',
		webServerRedirectUri: '回调地址',
		authorities: '权限',
		accessTokenValidity: '令牌时效（秒）',
		refreshTokenValidity: '刷新时效（秒）',
		additionalInformation: '扩展信息',
		autoapprove: '自动放行',
		createBy: '创建人',
		updateBy: '修改人',
		createTime: '上传时间',
		updateTime: '更新时间',
		tenantId: '所属租户',
		captchaFlag: '验证码开关',
		encFlag: '前端密码加密',
		onlineQuantity: '允许同时在线',
		inputIdTip: '请输入ID',
		inputClientIdTip: '请输入客户端id',
		inputResourceIdsTip: '请输入',
		inputClientSecretTip: '请输入密钥',
		inputScopeTip: '请输入域',
		inputAuthorizedGrantTypesTip: '请输入授权模式',
		inputWebServerRedirectUriTip: '请输入回调地址',
		inputAuthoritiesTip: '请输入权限',
		inputAccessTokenValidityTip: '请输入令牌时效',
		inputRefreshTokenValidityTip: '请输入刷新时效',
		inputAdditionalInformationTip: '请输入扩展信息',
		inputAutoapproveTip: '请输入自动放行',
		inputCreateByTip: '请输入创建人',
		inputUpdateByTip: '请输入修改人',
		inputCreateTimeTip: '请输入上传时间',
		inputUpdateTimeTip: '请输入更新时间',
		inputTenantIdTip: '请输入所属租户',
	},
};
