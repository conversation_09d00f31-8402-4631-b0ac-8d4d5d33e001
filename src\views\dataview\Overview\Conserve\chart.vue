<script setup>
// @ts-nocheck
import VChart from 'vue-echarts'
import * as echarts from 'echarts'
import 'echarts-liquidfill'
import { computed, ref, watch } from 'vue'; // 确保导入 computed 和 watch

const props = defineProps({
  data: {
    type: Object, // 更精确的类型定义
    required: true,
    default: () => ({ name: 'Loading...', value: '0%' }) // 更新默认值
  },
  color: { // 新增 color prop
    type: String,
    required: true
  }
})

// 从 props.data.value 中提取数值，例如 "11%" -> 11
const ratio = computed(() => parseInt(props.data.value));
const ratioLength = 3; //波浪数量

const option = ref({
  // backgroundColor: '#003366',
  title: {
    show: false, // 隐藏图表中心的标题
  },
  series: [{
    type: 'liquidFill',
    radius: '45%',
    center: ['50%', '50%'],
    color: [props.color, '#3831f3', props.color], // 初始化时使用 props.color
    data: Array(ratioLength).fill(0).map(() => ratio.value / 100), // 初始化 data
    backgroundStyle: {
      // borderWidth: 1,
      color: '#1f2c52'
    },
    label: {
      formatter: '', // 主标题显示百分比，副标题显示名称
    },
    outline: {
      show: false,
    }
  },
  { //细的外圈 - 修改为完整背景环
    type: "pie",
    center: ["50%", "50%"],
    radius: ["49%", "50%"],
    data: [{
      name: "",
      value: 100, // 完整圆环
      itemStyle: {
        color: '#41496b'
      },
      label: { show: false },
      labelLine: { show: false },
      emphasis: { // 保持emphasis效果，即使是背景
        itemStyle: {
          color: '#41496b'
        },
        labelLine: { show: false },
      }
    }]
  },
  { //粗的外圈 - 修改为逆时针填充
    type: "pie",
    center: ["50%", "50%"],
    radius: ["48%", "51%"],
    zlevel: 2,
    clockwise: false, // 逆时针填充
    startAngle: 90, // 从12点钟方向开始
    data: [
      { // 活动部分
        name: "",
        value: ratio.value, // 根据百分比设定值
        labelLine: {
          show: false
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: props.color
          },]),
        },
        emphasis: {
          labelLine: {
            show: false
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
              offset: 0,
              color: props.color
            }, {
              offset: 1,
              color: '#0707ff'
            }]),
          },
        }
      },
      { // 非活动部分 (透明)
        name: "",
        value: 100 - ratio.value, // 剩余部分
        itemStyle: {
          color: 'transparent'
        },
        label: { show: false },
        labelLine: { show: false },
        emphasis: {
          itemStyle: {
            color: 'transparent'
          },
          labelLine: { show: false },
        }
      }
    ]
  }
  ]
})

watch([() => props.data, () => props.color], ([newData, newColor]) => { // 监听 data 和 color
  const newRatio = parseInt(newData.value);

  option.value.series[0].data = Array(ratioLength).fill(0).map(() => newRatio / 100);
  option.value.series[0].color = [newColor, '#3831f3', newColor]; // 更新水球颜色

  // 更新粗外圈的值
  option.value.series[2].data[0].value = newRatio;
  option.value.series[2].data[1].value = 100 - newRatio;

  // 更新粗外圈颜色
  option.value.series[2].data[0].itemStyle.color = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
    offset: 0,
    color: newColor
  },]);
  option.value.series[2].data[0].emphasis.itemStyle.color = new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
    offset: 0,
    color: newColor
  }, {
    offset: 1,
    color: '#0707ff'
  }]);

}, { deep: true });

</script>

<template>
  <div class="w-full h-full relative">
    <!-- 图表区域，占用全部空间 -->
    <v-chart :option="option" autoresize class="w-full h-full" />
    <!-- 百分比文字，绝对定位在图表底部 -->
    <div class="absolute bottom-1 left-0 right-0 flex justify-center">
      <span class="text-white text-lg font-bold">{{ ratio }}%</span>
    </div>
  </div>
</template>
