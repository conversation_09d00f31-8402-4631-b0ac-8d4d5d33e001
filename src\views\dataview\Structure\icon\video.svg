<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="16" height="10" viewBox="0 0 16 10">
              <defs>
                <clipPath id="master_svg0_0_7747">
                  <rect x="0" y="0" width="16" height="10" rx="0" />
                </clipPath>
                <mask id="master_svg1_0_7750" style="mask-type:alpha" maskUnits="objectBoundingBox">
                  <g>
                    <g>
                      <path d="M0,0.0023200062569230795C0,0.0023200062569230795,16,0.0023200062569230795,16,0.0023200062569230795C16,0.0023200062569230795,16,10.000000006256924,16,10.000000006256924C16,10.000000006256924,0,10.000000006256924,0,10.000000006256924C0,10.000000006256924,0,0.0023200062569230795,0,0.0023200062569230795Z" fill="#000000" fill-opacity="1" />
                    </g>
                  </g>
                </mask>
              </defs>
              <g clip-path="url(#master_svg0_0_7747)">
                <g mask="url(#master_svg1_0_7750)">
                  <g>
                    <g>
                      <path d="M11.2939,3.0954C11.2939,3.14438,11.2409,3.22971,11.1359,3.35215C11.0299,3.4746,10.9168,3.61844,10.796,3.78496C10.6754,3.95097,10.5628,4.13193,10.4571,4.32758C10.3511,4.52324,10.2992,4.72353,10.2992,4.92898C10.2992,5.13443,10.3514,5.33241,10.4571,5.52316C10.5628,5.71392,10.6754,5.8874,10.796,6.04387C10.9168,6.2006,11.0299,6.33001,11.1359,6.43286C11.2409,6.5352,11.2939,6.60145,11.2939,6.63084C11.2939,6.63084,11.2939,8.17106,11.2939,8.17106C11.2939,8.33733,11.2313,8.52551,11.1052,8.73585C10.9801,8.94594,10.8243,9.14392,10.638,9.33003C10.4526,9.51563,10.2462,9.67004,10.0196,9.79171C9.79385,9.91416,9.58002,9.97525,9.37864,9.97525C9.37864,9.97525,1.88474,9.97525,1.88474,9.97525C1.61341,9.97525,1.3646,9.92885,1.13884,9.83605C0.912295,9.74325,0.714096,9.61333,0.54266,9.44758C0.372019,9.28106,0.238474,9.08566,0.143084,8.86062C0.0476948,8.63558,0,8.3912,0,8.12724C0,8.12724,0,1.80445,0,1.80445C0,1.60905,0.04531,1.40592,0.135665,1.19558C0.22655,0.985229,0.354001,0.792411,0.520403,0.616091C0.686539,0.440028,0.882618,0.293352,1.10837,0.176063C1.33466,0.0587735,1.58294,0,1.8548,0C1.8548,0,9.34897,0,9.34897,0C9.6203,0,9.8744,0.0464001,10.1105,0.139458C10.3468,0.232258,10.5525,0.359601,10.7284,0.520712C10.9043,0.682082,11.0429,0.875158,11.1436,1.1002C11.2443,1.32524,11.2941,1.56472,11.2941,1.81914C11.2941,1.81914,11.2941,3.0954,11.2941,3.0954C11.2941,3.0954,11.2939,3.0954,11.2939,3.0954ZM15.9836,1.71629C15.9836,1.71629,15.9836,8.42033,15.9836,8.42033C15.9836,8.63532,15.9253,8.82608,15.81,8.99234C15.6945,9.15887,15.5209,9.24187,15.2899,9.24187C15.2096,9.24187,15.1065,9.21249,14.9807,9.15397C14.8511,9.09313,14.7252,9.02457,14.6044,8.94852C14.4785,8.87041,14.3601,8.79205,14.2496,8.71369C14.1394,8.63558,14.0588,8.57191,14.0082,8.52319C13.8778,8.41544,13.6789,8.22958,13.4128,7.96587C13.146,7.70165,12.8776,7.4034,12.6057,7.07112C12.3344,6.73859,12.0959,6.39626,11.8895,6.04413C11.6839,5.69226,11.5811,5.36927,11.5811,5.07592C11.5811,4.78256,11.6913,4.44771,11.9128,4.07109C12.1343,3.69474,12.4001,3.32534,12.7117,2.96342C13.0231,2.6015,13.3503,2.26639,13.6916,1.9586C14.0336,1.65056,14.3198,1.41804,14.5517,1.26183C14.6418,1.20305,14.7647,1.13216,14.9205,1.04916C15.0763,0.965896,15.2149,0.924394,15.3357,0.924394C15.6068,0.924394,15.783,0.99786,15.8633,1.14454C15.9438,1.29121,15.9841,1.47217,15.9841,1.68716C15.9841,1.68716,15.9841,1.71655,15.9841,1.71655C15.9841,1.71655,15.9836,1.71629,15.9836,1.71629Z" fill="#FFFFFF" fill-opacity="1" />
                    </g>
                  </g>
                </g>
              </g>
            </svg>