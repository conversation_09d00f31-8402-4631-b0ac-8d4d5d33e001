<script setup lang='ts'>
defineProps({
  title: String,
})
</script>

<template>
  <div class='w-[420px] relative top-[10px] mb-4'>
    <header class="w-full h-[46.5px] pl-8 pb-1 header flex items-center justify-between">
      <!-- <img class="absolute z-0" src="/image/Frame 427321880.png" alt=""> -->
      <div class="ff">
        <div>{{ title }}</div>
      </div>
      <slot name="header"></slot>
    </header>
    <div class="mt-1 bg-[#031133] bgi">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
@font-face {
  font-family: '优设标题黑';
  src: url('/assets/fonts/优设标题黑.ttf');
}
.header{
  background: url('/image/Frame 427321880.png') no-repeat center center;
  background-size: 100% 100%;
}
.bgi{
  background: url('/image/Frame 427318825.png') no-repeat center center;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
}
.ff {
  font-family: '优设标题黑';
  font-size: 26px;
  font-weight: normal;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.04em;
  background: linear-gradient(180deg, #FFFFFF 27%, #FFFFFF 43%, #61AEF9 76%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>