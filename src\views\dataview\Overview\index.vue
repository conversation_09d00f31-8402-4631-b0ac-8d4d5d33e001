<script setup>
import Card from '../Card/index.vue'
import Sum from './Sum/index.vue'
import Radar from './Radar/index.vue'
import Health from './Health/index.vue'
import Technical from './Technical/index.vue'
import Alarm from './Alarm/index.vue'
import Monitor from './Monitor/index.vue'
import Conserve from './Conserve/index.vue'
const activeName = ref('Health')
</script>

<template>
  <div class="w-[440px] h-full absolute top-5 left-[40px] z-10">
    <Card title="资产概览">
      <Sum />
    </Card>
    <Card title="综合指标">
      <div class="w-full h-[228px]">
        <Radar />
      </div>
    </Card>
    <!-- 顶部tabs导航 -->

    <!-- 根据tab显示不同的卡片内容 -->
    <Card title="健康概览">
      <template #header>
        <el-radio-group type="button" v-model="activeName" class="custom-radio-group">
          <el-radio-button label="Health">健康状况</el-radio-button>
          <el-radio-button label="Technical">技术状况</el-radio-button>
        </el-radio-group>
      </template>
      <Health v-if="activeName === 'Health'" />
      <Technical v-else />
    </Card>
  </div>
  <!-- right -->
  <div class="w-[440px] h-full absolute top-5 right-[40px]">
    <Card title="预警概览">
      <Alarm />
    </Card>
    <Card title="监测概览">
      <Monitor />
    </Card>
    <Card title="养护概览">
      <Conserve />
    </Card>
  </div>
</template>

<style scoped>
.custom-radio-group :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #258EFF; /* 浅蓝色背景 */
  border-color: #258EFF;
  color: #fff; /* 选中时深色字体 */
  box-shadow: -1px 0 0 0 #258EFF;
}

.custom-radio-group :deep(.el-radio-button__inner) {
  color: #fFF; /* 默认状态浅蓝色字体 */
  background-color: rgba(29, 155, 240, 0);
  border-color: #2E76C3;
}
</style>