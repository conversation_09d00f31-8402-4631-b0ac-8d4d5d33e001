@import 'mixins/index.scss';

/* Button 按钮
------------------------------- */
// 第三方字体图标大小
.el-button:not(.is-circle) i.el-icon,
.el-button i.iconfont,
.el-button i.fa,
.el-button--default i.iconfont,
.el-button--default i.fa {
	font-size: 14px !important;
}

.el-button--small i.iconfont,
.el-button--small i.fa {
	font-size: 12px !important;
}
.el-button [class*='el-icon'] + span {
	margin-left: 6px;
}
/* Input 输入框、InputNumber 计数器
------------------------------- */
// 菜单搜索
.el-autocomplete-suggestion__wrap {
	max-height: 280px !important;
}

/* Form 表单
------------------------------- */
.el-form {
	// 用于修改弹窗时表单内容间隔太大问题，如系统设置的新增菜单弹窗里的表单内容
	.el-form-item:last-of-type {
		margin-bottom: 0 !important;
	}

	// 修复行内表单最后一个 el-form-item 位置下移问题
	&.el-form--inline {
		.el-form-item--large.el-form-item:last-of-type {
			margin-bottom: 22px !important;
		}

		.el-form-item--default.el-form-item:last-of-type,
		.el-form-item--small.el-form-item:last-of-type {
			margin-bottom: 18px !important;
		}
		// 修改表单项默认右边距
		.el-form-item {
			margin-right: 12px;
		}
	}

	.el-form-item .el-form-item__label .el-icon {
		margin-right: 0px;
	}
}

// 修改数字输入框默认宽度为100%
.el-input-number {
	width: 100%;
}

// https://github.com/element-plus/element-plus/pull/15352
.el-form--inline {
	.el-form-item {
		& > .el-input,
		.el-cascader,
		.el-select,
		.el-date-editor,
		.el-autocomplete {
			width: 240px;
		}
	}
}

/* Alert 警告
------------------------------- */
.el-alert {
	border: 1px solid;
}

.el-alert__title {
	word-break: break-all;
}

/* Message 消息提示
------------------------------- */
.el-message {
	min-width: unset !important;
	padding: 12px 20px !important;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
	border-width: 1px;
	border-style: solid;
	border-radius: 4px;

	.el-message__content {
		font-size: 14px;
		line-height: 1.4;
		color: var(--el-text-color-regular);
	}

	.el-message__icon {
		margin-right: 8px;
		font-size: 16px;
		vertical-align: middle;
	}

	// 成功类型
	&.el-message--success {
		background-color: #f0f9eb;
		border-color: #67c23a;
		.el-message__content {
			color: #67c23a;
		}
	}

	// 警告类型
	&.el-message--warning {
		background-color: #fdf6ec;
		border-color: #e6a23c;
		.el-message__content {
			color: #e6a23c;
		}
	}

	// 错误类型
	&.el-message--error {
		background-color: #fef0f0;
		border-color: #f56c6c;
		.el-message__content {
			color: #f56c6c;
		}
	}

	// 信息类型
	&.el-message--info {
		background-color: #f4f4f5;
		border-color: #909399;
		.el-message__content {
			color: #909399;
		}
	}
}

/* NavMenu 导航菜单
------------------------------- */
// 鼠标 hover 时颜色
.el-menu-hover-bg-color {
	background-color: var(--next-bg-menuBarActiveColor) !important;
}

// 默认样式修改
.el-menu {
	border-right: none !important;
	width: 100%;
}

.el-menu-item {
	height: 56px !important;
	line-height: 56px !important;
}

.el-menu-item,
.el-sub-menu__title {
	color: var(--next-bg-menuBarColor) !important;
}

// 修复点击左侧菜单折叠再展开时，宽度不跟随问题
.el-menu--collapse {
	width: 64px !important;
}

// 外部链接时
.el-menu-item a,
.el-menu-item a:hover,
.el-menu-item i,
.el-sub-menu__title i {
	color: inherit;
	text-decoration: none;
}

// 第三方图标字体间距/大小设置
.el-menu-item .iconfont,
.el-sub-menu .iconfont,
.el-menu-item .fa,
.el-menu-item .svg-icon,
.el-sub-menu .svg-icon,
.el-sub-menu .fa {
	@include generalIcon;
}

// 水平菜单、横向菜单高亮 背景色，鼠标 hover 时，有子级菜单的背景色
.el-menu-item.is-active,
.el-sub-menu.is-active .el-sub-menu__title,
.el-sub-menu:not(.is-opened):hover .el-sub-menu__title {
	@extend .el-menu-hover-bg-color;
}

.el-menu-item:hover {
	@extend .el-menu-hover-bg-color;
}

.el-sub-menu.is-active.is-opened .el-sub-menu__title {
	background-color: unset !important;
}

// 水平菜单、横向菜单折叠 a 标签
.el-popper.is-dark a {
	color: var(--el-color-white) !important;
	text-decoration: none;
}

// 横向菜单（经典、横向）布局
.el-menu.el-menu--horizontal {
	border-bottom: none !important;
	width: 100% !important;

	// 横向布局默认选中的字体颜色同步为左侧菜单颜色（默认黑色）
	.el-sub-menu.is-active .el-sub-menu__title {
		color: var(--next-bg-menuBarColor) !important;
	}

	// 经典布局，当选中状态的时候字体颜色同步为左侧菜单颜色（默认黑色）
	.el-menu-item,
	.el-sub-menu__title {
		height: 50px !important;
		color: var(--next-bg-topBarColor) !important;
		&:hover,
		&.is-active {
			color: var(--next-bg-menuBarColor) !important;
		}
	}

	.el-menu-item:not(.is-active):hover,
	.el-sub-menu:not(.is-active):hover .el-sub-menu__title {
		color: var(--next-bg-topBarColor);
	}
}

// 添加弹出子菜单容器的样式
.el-menu--horizontal.el-menu--popup-container {
	.el-menu.el-menu--popup {
		.el-menu-item,
		.el-sub-menu__title {
			height: 56px !important;
			line-height: 56px !important;
		}
	}
}

/* Tabs 标签页
------------------------------- */
.el-tabs__nav-wrap::after {
	height: 1px !important;
}

/* Dropdown 下拉菜单
------------------------------- */
.el-dropdown-menu {
	list-style: none !important;
	/*修复 Dropdown 下拉菜单样式问题 2022.03.04*/
}

.el-dropdown-menu .el-dropdown-menu__item {
	white-space: nowrap;

	&:not(.is-disabled):hover {
		background-color: var(--el-dropdown-menuItem-hover-fill);
		color: var(--el-dropdown-menuItem-hover-color);
	}
}

/* Steps 步骤条
------------------------------- */
.el-step__icon-inner {
	font-size: 30px !important;
	font-weight: 400 !important;
}

.el-step__title {
	font-size: 14px;
}

/* Dialog 对话框
------------------------------- */
.el-overlay {
	overflow: hidden;

	.el-overlay-dialog {
		display: flex;
		align-items: center;
		justify-content: center;
		position: unset !important;
		width: 100%;
		height: 100%;

		.el-dialog {
			margin: 0 auto !important;
			position: absolute;
			border-radius: 8px;
			box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
			overflow: hidden;

			.el-dialog__header {
				margin: 0;
				padding: 16px 20px;

				.el-dialog__title {
					font-size: 16px;
					font-weight: 600;
					color: var(--el-text-color-primary);
				}

				.el-dialog__headerbtn {
					top: 20px;
					right: 20px;

					.el-dialog__close {
						font-size: 18px;
						transition: all 0.3s ease;

						&:hover {
							transform: rotate(90deg);
							color: var(--el-color-primary);
						}
					}
				}
			}

			.el-dialog__body {
				// padding: 20px !important;
				// max-height: calc(90vh - 120px) !important;
				overflow-y: auto;
				overflow-x: hidden;
				color: var(--el-text-color-regular);
			}

			.el-dialog__footer {
				padding: 12px 20px;

				.el-button {
					padding: 8px 20px;
					min-width: 80px;
				}
			}
		}
	}
}

.el-dialog {
	--el-transition-duration: 0.3s;

	&.dialog-fade-enter-active {
		animation: dialog-fade-in var(--el-transition-duration);
	}

	&.dialog-fade-leave-active {
		animation: dialog-fade-out var(--el-transition-duration);
	}
}

@keyframes dialog-fade-in {
	0% {
		transform: translate3d(0, -30px, 0);
		opacity: 0;
	}
	100% {
		transform: translate3d(0, 0, 0);
		opacity: 1;
	}
}

@keyframes dialog-fade-out {
	0% {
		transform: translate3d(0, 0, 0);
		opacity: 1;
	}
	100% {
		transform: translate3d(0, -30px, 0);
		opacity: 0;
	}
}

/* Card 卡片
------------------------------- */
.el-card__header {
	padding: 15px 20px;
}

// 日历
.el-calendar-table .el-calendar-day {
	height: 50px;
	padding: 0;
}

/* Table 表格 element plus 2.2.0 版本
------------------------------- */
// 表格修改默认颜色
.el-table {
	color: #000000;
	.el-button.is-text {
		padding: 0;
	}
}

/* scrollbar
------------------------------- */
.el-scrollbar__bar {
	z-index: 4;
}

.el-scrollbar__wrap {
	max-height: 100%;
}

.el-select-dropdown .el-scrollbar__wrap {
	overflow-x: scroll !important;
}

/*修复Select 选择器高度问题*/
.el-select-dropdown__wrap {
	max-height: 274px !important;
}

/*修复Cascader 级联选择器高度问题*/
.el-cascader-menu__wrap.el-scrollbar__wrap {
	height: 204px !important;
}

/*用于界面高度自适应（main.vue），区分 scrollbar__view，防止其它使用 scrollbar 的地方出现滚动条消失*/
.layout-container-view .el-scrollbar__view {
	height: 100%;
}

/*防止分栏布局二级菜单很多时，滚动条消失问题*/
.layout-columns-warp .layout-aside .el-scrollbar__view {
	height: unset !important;
}

/* Pagination 分页
------------------------------- */
.el-pagination__editor {
	margin-right: 8px;
}

.el-pagination {
	margin-top: 15px;
	justify-content: flex-end;
}

/*深色模式时分页高亮问题*/
.el-pagination.is-background .btn-next.is-active,
.el-pagination.is-background .btn-prev.is-active,
.el-pagination.is-background .el-pager li.is-active {
	background-color: var(--el-color-primary) !important;
	color: var(--el-color-white) !important;
}

.el-empty {
	--el-empty-padding: 0 0 !important;
}

// 防止被tailwindcss默认样式覆盖
svg {
	display: inline;
	vertical-align: baseline;
}

// 覆盖默认样式宽度
.el-date-editor.el-input, .el-date-editor.el-input__wrapper {
	width: 100%;
}

// 表单设计器元素布局增加间距
.fc-form-row .fc-form-col {
	margin-bottom: 18px;
}
