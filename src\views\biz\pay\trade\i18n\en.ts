export default {
	trade: {
		index: '#',
		importpayTradeOrderTip: 'import PayTradeOrder',
		orderId: 'orderId',
		channelId: 'channelId',
		amount: 'amount',
		currency: 'currency',
		status: 'status',
		clientIp: 'clientIp',
		device: 'device',
		subject: 'subject',
		body: 'body',
		extra: 'extra',
		channelMchId: 'channelMchId',
		channelOrderNo: 'channelOrderNo',
		errCode: 'errCode',
		errMsg: 'errMsg',
		param1: 'param1',
		param2: 'param2',
		notifyUrl: 'notifyUrl',
		notifyCount: 'notifyCount',
		lastNotifyTime: 'lastNotifyTime',
		expireTime: 'expireTime',
		paySuccTime: 'paySuccTime',
		createTime: 'createTime',
		updateTime: 'updateTime',
		delFlag: 'delFlag',
		tenantId: 'tenantId',
		inputOrderIdTip: 'input orderId',
		inputChannelIdTip: 'input channelId',
		inputAmountTip: 'input amount',
		inputCurrencyTip: 'input currency',
		inputStatusTip: 'input status',
		inputClientIpTip: 'input clientIp',
		inputDeviceTip: 'input device',
		inputSubjectTip: 'input subject',
		inputBodyTip: 'input body',
		inputExtraTip: 'input extra',
		inputChannelMchIdTip: 'input channelMchId',
		inputChannelOrderNoTip: 'input channelOrderNo',
		inputErrCodeTip: 'input errCode',
		inputErrMsgTip: 'input errMsg',
		inputParam1Tip: 'input param1',
		inputParam2Tip: 'input param2',
		inputNotifyUrlTip: 'input notifyUrl',
		inputNotifyCountTip: 'input notifyCount',
		inputLastNotifyTimeTip: 'input lastNotifyTime',
		inputExpireTimeTip: 'input expireTime',
		inputPaySuccTimeTip: 'input paySuccTime',
		inputCreateTimeTip: 'input createTime',
		inputUpdateTimeTip: 'input updateTime',
		inputDelFlagTip: 'input delFlag',
		inputTenantIdTip: 'input tenantId',
		refundBtn: 'refund',
	},
};
