// 隧道管理配置

// 导入共享选项数据
import {
  tunnelSceneOptions as sceneOptions,
  techStatusOptions,
  designGradeOptions,
  crossingObjectOptions
} from './common-options.js'

// 导入配置工厂函数
import { createStructureConfig } from './config-factory.js'

// 隧道类型选项
const tunnelTypeOptions = [
  { label: '公路隧道', value: '公路隧道' },
  { label: '铁路隧道', value: '铁路隧道' },
  { label: '地铁隧道', value: '地铁隧道' },
  { label: '水工隧道', value: '水工隧道' },
  { label: '市政隧道', value: '市政隧道' }
]

// 隧道等级选项
const tunnelGradeOptions = [
  { label: '特长隧道', value: '特长隧道' },
  { label: '长隧道', value: '长隧道' },
  { label: '中隧道', value: '中隧道' },
  { label: '短隧道', value: '短隧道' }
]

// 通风方式选项
const ventilationOptions = [
  { label: '自然通风', value: '自然通风' },
  { label: '机械通风', value: '机械通风' },
  { label: '半横向通风', value: '半横向通风' },
  { label: '全横向通风', value: '全横向通风' },
  { label: '纵向通风', value: '纵向通风' }
]

// 隧道特有的结构数据字段
const tunnelStructureFields = [
  { 
    label: '隧道类型', 
    prop: 'tunnelType', 
    type: 'select', 
    dicData: tunnelTypeOptions, 
    placeholder: '请选择隧道类型' 
  },
  { 
    label: '隧道长度', 
    prop: 'tunnelLength', 
    placeholder: '请输入隧道长度' 
  },
  { 
    label: '隧道宽度', 
    prop: 'tunnelWidth', 
    placeholder: '请输入隧道宽度' 
  },
  { 
    label: '隧道净高', 
    prop: 'tunnelClearHeight', 
    placeholder: '请输入隧道净高' 
  },
  {
    label: '技术等级',
    prop: 'technicalGrade',
    type: 'select',
    dicData: techStatusOptions,
    placeholder: '请选择技术等级'
  },
  {
    label: '养护等级',
    prop: 'designGrade',
    type: 'select',
    dicData: designGradeOptions,
    placeholder: '请选择养护等级'
  },
  {
    label: '跨越地物类型',
    prop: 'crossingObjectType',
    type: 'select',
    dicData: crossingObjectOptions,
    placeholder: '请选择跨越地物类型'
  },
  { label: '跨越地物名称', prop: 'crossingObjectName', placeholder: '请输入跨越地物名称' },
  {
    label: '图片',
    prop: 'imageUrl',
    type: 'upload',
    listType: 'picture-img',
    editDisabled: true,
    placeholder: '请输入图片'
  }
]

// 使用配置工厂创建隧道配置
const tunnelConfig = createStructureConfig({
  structureType: '隧道',
  monitorScenariosConfig: { dicData: sceneOptions },
  baseDataOptions: {
    // 添加隧道特有的基础字段
    additionalFields: [
      { 
        label: '监测场景', 
        prop: 'monitorScenarios', 
        type: 'select', 
        dicData: sceneOptions, 
        placeholder: '请选择监测场景' 
      }
    ],
    // 自定义字段标签
    customFields: {
      structureUniqueCode: { 
        label: '隧道编号', 
        placeholder: '请输入隧道编号' 
      },
      structureLocation: { 
        label: '隧道位置', 
        placeholder: '请输入隧道位置' 
      }
    }
  },
  structureDataFields: tunnelStructureFields,
  // 添加额外的搜索表单字段
  additionalSearchFormFields: {
    tunnelType: ''
  }
})

// 导出隧道表格配置
export const tunnelOption = tunnelConfig.tableConfig

// 导出搜索表单初始值
export const tunnelSearchForm = tunnelConfig.searchForm

// 导出隧道特有的选项数据（供其他地方使用）
export { 
  tunnelTypeOptions, 
  tunnelGradeOptions, 
  ventilationOptions 
}
