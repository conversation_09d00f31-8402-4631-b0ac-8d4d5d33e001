export default {
	gen: {
		syncBtn: '同步',
		designBtn: '设计',
		genBtn: '生成',
		prewBtn: '预览',
	},
	table: {
		index: '#',
		importTableTip: '导入列属性',
		id: 'id',
		tableName: '表名',
		className: '类名',
		tableComment: '说明',
		tableDesc: '注释',
		author: '作者',
		email: '邮箱',
		packageName: '项目包名',
		version: '项目版本号',
		generatorType: '生成方式  0：zip压缩包   1：自定义目录',
		backendPath: '后端生成路径',
		frontendPath: '前端生成路径',
		moduleName: '模块名',
		functionName: '功能名',
		formLayout: '表单布局  1：一列   2：两列',
		datasourceId: '数据源ID',
		baseclassId: '基类ID',
		createTime: '创建时间',
		inputidTip: '请输入id',
		inputtableNameTip: '请输入表名',
		inputclassNameTip: '请输入类名',
		inputtableCommentTip: '请输入说明',
		inputauthorTip: '请输入作者',
		inputemailTip: '请输入邮箱',
		inputpackageNameTip: '请输入项目包名',
		inputversionTip: '请输入项目版本号',
		inputgeneratorTypeTip: '请输入生成方式  0：zip压缩包   1：自定义目录',
		inputbackendPathTip: '请输入后端生成路径',
		inputfrontendPathTip: '请输入前端生成路径',
		inputmoduleNameTip: '请输入模块名',
		inputfunctionNameTip: '请输入功能名',
		inputformLayoutTip: '请输入表单布局  1：一列   2：两列',
		inputdatasourceIdTip: '请输入数据源ID',
		inputbaseclassIdTip: '请输入基类ID',
		inputcreateTimeTip: '请输入创建时间',
	},
};
