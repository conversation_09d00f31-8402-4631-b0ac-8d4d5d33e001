<template>
  <AvTable
    :option="tunnelOption"
    :searchForm="tunnelSearchForm"
    :listApi="queryTunnels"
    :detailApi="querySingleStructureInfo"
    :updateApi="addAndUpdate"
    @search="handleCustomSearch"
    @load="handleCustomLoad"
    @update="handleCustomUpdate"
  />
</template>

<script setup>
import AvTable from '../AvTable.vue'
import { tunnelOption, tunnelSearchForm } from './configs/tunnel-config.js'
import { queryTunnels, querySingleStructureInfo, addAndUpdate } from '/@/api/dataview/structure'

// 自定义搜索处理（可选）
function handleCustomSearch(params) {
  console.log('隧道搜索参数:', params)
  // 可以在这里添加特殊的搜索逻辑
  // 添加隧道类型过滤条件
  params.structureType = 2 // 隧道的结构类型为2
}

// 自定义加载处理（可选）
function handleCustomLoad() {
  console.log('隧道数据加载')
  // 可以在这里添加特殊的加载逻辑
}

// 自定义更新处理（可选）
function handleCustomUpdate(form, done) {
  console.log('隧道数据更新:', form)
  // 可以在这里添加特殊的更新逻辑
  done()
}
</script>

<style>

</style>