<template>
  <div v-if="selectedDevices.length > 0" class="w-full h-full">
    <div v-if="zoomedIndexCode" class="w-full h-full relative">
      <Video v-if="zoomedVideo" :type="zoomedVideo.deviceType" :deviceSerial="zoomedVideo.id" :label="zoomedVideo.deviceName" class="w-full h-full" :is-zoomed="true" @zoom="zoomedIndexCode = null" />
    </div>
    <div v-else class="grid grid-cols-2 gap-2 w-full h-full">
      <div v-for="item in selectedDevices.slice(0, 2)" :key="item.id" class="aspect-video">
        <Video :deviceSerial="item.id" :type="item.deviceType" :label="item.deviceName" class="w-full h-full" @zoom="handleZoom" />
      </div>
    </div>
  </div>
  <div v-else class="flex items-center justify-center h-full text-gray-400">
    暂无视频设备
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Video from './Video.vue'

const props = defineProps({
  selectedDevices: {
    type: Array,
    default: () => [],
  },
})

const zoomedIndexCode = ref(null)

const zoomedVideo = computed(() => {
  if (!zoomedIndexCode.value) return null
  console.log(props.selectedDevices.find((d) => d.id === zoomedIndexCode.value));
  
  return props.selectedDevices.find((d) => d.id === zoomedIndexCode.value)
})

function handleZoom(deviceSerial) {
  zoomedIndexCode.value = deviceSerial
}
</script> 