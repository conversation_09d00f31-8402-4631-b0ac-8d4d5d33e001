<template>
  <div class="cards-container-base bg-custom-gradient-container p-4 pb-1 max-md:p-3 font-sans grid grid-cols-3 mx-40 max-lg:grid-cols-1 gap-4 max-lg:gap-3">
    <!-- 设备状态卡片 -->
    <div class="card-base status-card-custom group card-custom-bg card-custom-shadow hover:card-custom-shadow-hover card-top-highlight p-4 max-lg:p-2 rounded-2xl transition-all duration-300 ease-in-out hover:-translate-y-1 flex flex-col max-lg:min-h-[100px] border border-[rgba(59,130,246,0.3)]">
      <div class="flex justify-between items-center mb-3 max-lg:mb-1.5">
        <h3 class="card-title-base text-slate-50 text-base max-lg:text-sm font-semibold">健康度</h3>
      </div>
      <div class="flex flex-col items-center text-center justify-center flex-grow">
        <div class="w-6 h-6 rounded-full mb-3 max-md:w-12 max-md:h-12 max-md:mb-2 max-lg:w-10 max-lg:h-10 max-lg:mb-0 relative flex items-center justify-center" :style="{ backgroundColor: currentStatus.color, boxShadow: `0 0 20px ${currentStatus.color}` }">
          <div class="status-pulse-base absolute w-full h-full rounded-full opacity-60" :style="{ backgroundColor: currentStatus.color }"></div>
        </div>
        <div class="text-slate-50 max-lg:hidden">
          <div class="status-name-base text-[15px] font-bold mb-1 max-md:text-base max-lg:text-sm max-lg:mb-0.5">{{ currentStatus.label }}</div>
          <!-- <div class="text-sm text-slate-400 opacity-80 max-lg:text-xs max-lg:leading-tight">{{ currentStatus.desc }}</div> -->
        </div>
      </div>
    </div>

    <!-- 设备处置率卡片 -->
    <div class="card-base metric-card-custom card-custom-bg card-custom-shadow hover:card-custom-shadow-hover card-top-highlight p-4 max-lg:p-2 rounded-2xl transition-all duration-300 ease-in-out hover:-translate-y-1 flex flex-col max-lg:min-h-[100px] border border-[rgba(59,130,246,0.3)]">
      <div class="flex justify-between items-center mb-3 max-lg:mb-1.5">
        <h3 class="card-title-base text-slate-50 text-base max-lg:text-sm font-semibold">预警处置率</h3>
      </div>
      <div class="flex flex-col justify-between flex-grow">
        <div class="flex items-baseline justify-center mb-3 max-lg:mb-1.5">
          <span class="number-base text-3xl font-bold text-slate-50 leading-none max-md:text-2xl max-lg:text-xl">{{ disposalRate }}</span>
          <span class="text-lg font-semibold text-blue-500 ml-1 max-md:text-sm max-lg:text-sm">%</span>
        </div>
        <div class="relative h-2 my-3 max-lg:h-1 max-lg:my-1.5">
          <div class="absolute w-full h-full bg-blue-500/20 rounded-[4px]"></div>
          <div class="bar-fill-base absolute h-full rounded-[4px] transition-width duration-[1500ms] ease-out disposal-bar-custom" :style="{ width: disposalRate + '%', boxShadow: '0 0 8px #3b82f6' }"></div>
        </div>
        <!-- <div class="text-slate-400 text-sm text-center font-medium max-lg:text-xs max-lg:leading-tight">处置完成率</div> -->
      </div>
    </div>

    <!-- 设备在线率卡片 -->
    <div class="card-base metric-card-custom card-custom-bg card-custom-shadow hover:card-custom-shadow-hover card-top-highlight p-4 max-lg:p-2 rounded-2xl transition-all duration-300 ease-in-out hover:-translate-y-1 flex flex-col max-lg:min-h-[100px] border border-[rgba(59,130,246,0.3)]">
      <div class="flex justify-between items-center mb-3 max-lg:mb-1.5">
        <h3 class="card-title-base text-slate-50 text-base max-lg:text-sm font-semibold">设备在线率</h3>
      </div>
      <div class="flex flex-col justify-between flex-grow">
        <div class="flex items-baseline justify-center mb-3 max-lg:mb-1.5">
          <span class="number-base text-3xl font-bold text-slate-50 leading-none max-md:text-2xl max-lg:text-xl">{{ onlineRate }}</span>
          <span class="text-lg font-semibold text-blue-500 ml-1 max-md:text-sm max-lg:text-sm">%</span>
        </div>
        <div class="relative h-2 my-3 max-lg:h-1 max-lg:my-1.5">
          <div class="absolute w-full h-full bg-blue-500/20 rounded-[4px]"></div>
          <div class="bar-fill-base absolute h-full rounded-[4px] transition-width duration-[1500ms] ease-out online-bar-custom" :style="{ width: onlineRate + '%', boxShadow: '0 0 8px #10b981' }"></div>
        </div>
        <!-- <div class="text-slate-400 text-sm text-center font-medium max-lg:text-xs max-lg:leading-tight">实时在线率</div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTransition } from '@vueuse/core'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      healthDegree: 0,
      disposalRate: 0,
      deviceOnlineRate: 0,
    }),
  },
})

const targetDisposal = computed(() => (props.data?.disposalRate ?? 0) * 100)
const disposalRate = useTransition(targetDisposal, {
  duration: 1500,
  transition: (n) => parseFloat(n.toFixed(1)),
})

const targetOnline = computed(() => (props.data?.deviceOnlineRate ?? 0) * 100)
const onlineRate = useTransition(targetOnline, {
  duration: 1500,
  transition: (n) => parseFloat(n.toFixed(1)),
})

// 设备状态选项
const statusOptions = [
  {
    label: '基本完好',
    color: '#10b981',
    desc: '设备运行正常'
  },
  {
    label: '轻微异常',
    color: '#f59e0b',
    desc: '需要关注监控'
  },
  {
    label: '中等异常',
    color: '#f97316',
    desc: '建议及时处理'
  },
  {
    label: '严重异常',
    color: '#ef4444',
    desc: '需要立即处理'
  }
]

// 当前状态
const currentStatus = computed(() => {
  const index = props.data?.healthDegree ?? 0
  return statusOptions[index] || statusOptions[0]
})
</script>

<style scoped>
.cards-container-base {
  height: 100%;
}

.bg-custom-gradient-container {
  /* background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); */
}

.card-base {
  position: relative;
  overflow: hidden;
}

.card-custom-bg {
  /* background: linear-gradient(145deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.95)); */
}

.card-custom-shadow {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-custom-shadow-hover:hover {
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(59, 130, 246, 0.2);
}

.card-top-highlight::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent);
}

.card-title-base {
  text-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.trend-up-base {
  animation: bounce 1s infinite;
}

.status-pulse-base {
  animation: pulse-wave 1s infinite;
}

.status-name-base {
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.number-base {
  text-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

.bar-fill-base {}

.disposal-bar-custom {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

.online-bar-custom {
  background: linear-gradient(90deg, #10b981, #3b82f6);
}

@keyframes pulse-wave {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }

  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-3px);
  }

  60% {
    transform: translateY(-1px);
  }
}
</style>