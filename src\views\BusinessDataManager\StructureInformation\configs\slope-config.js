// 边坡管理配置

// 导入共享选项数据
import {
  slopeSceneOptions,
  slopeTypeOptions,
  riskLevelOptions,
  supportMethodOptions,
  techStatusOptions,
  designGradeOptions
} from './common-options.js'

// 导入配置工厂函数
import { createStructureConfig } from './config-factory.js'

// 边坡特有的结构数据字段
const slopeStructureFields = [
  { label: '边坡类型', prop: 'slopeType', type: 'select', dicData: slopeTypeOptions, placeholder: '请选择边坡类型' },
  { label: '边坡高度', prop: 'slopeHeight', placeholder: '请输入边坡高度' },
  { label: '边坡长度', prop: 'slopeWidth', placeholder: '请输入边坡长度' },
  { label: '边坡坡度', prop: 'slopeGradient', placeholder: '请输入边坡坡度' },
  {
    label: '技术等级',
    prop: 'technicalGrade',
    type: 'select',
    dicData: techStatusOptions,
    placeholder: '请选择技术等级'
  },
  {
    label: '养护等级',
    prop: 'designGrade',
    type: 'select',
    dicData: designGradeOptions,
    placeholder: '请选择养护等级'
  },
  { label: '支护方式', prop: 'slopeSupportMethod', type: 'select', dicData: supportMethodOptions, placeholder: '请选择支护方式' },
  { label: '风险等级', prop: 'riskLevel', type: 'select', dicData: riskLevelOptions, placeholder: '请选择风险等级' },
  // slope_lithology 边坡岩性
  { label: '边坡岩性', prop: 'slopeLithology', placeholder: '请输入边坡岩性' },
  {
    label: '图片',
    prop: 'imageUrl',
    type: 'upload',
    listType: 'picture-img',
    editDisabled: true,
    placeholder: '请输入图片'
  }
]

// 使用配置工厂创建边坡配置
const slopeConfig = createStructureConfig({
  structureType: '边坡',
  monitorScenariosConfig: { dicData: slopeSceneOptions },
  baseDataOptions: {
    additionalFields: [
      { label: '监测场景', prop: 'monitorScenarios', type: 'select', dicData: slopeSceneOptions, placeholder: '请选择监测场景' }
    ],
    customFields: {
      structureUniqueCode: { label: '边坡编号', prop: 'slopeNumber', placeholder: '请输入边坡编号' },
      structureLocation: { label: '边坡位置', placeholder: '请输入边坡位置' }
    }
  },
  structureDataFields: slopeStructureFields,
  additionalSearchFormFields: {
    riskLevel: ''
  }
})

// 导出边坡表格配置
export const slopeOption = slopeConfig.tableConfig

// 导出搜索表单初始值
export const slopeSearchForm = slopeConfig.searchForm
