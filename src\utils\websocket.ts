import { Session } from '/@/utils/storage';
import other from './other';

export interface WebSocketOptions {
  uri: string;
  onMessage?: (data: any) => void;
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  autoReconnect?: boolean;
  maxReconnect?: number;
  heartbeatInterval?: number;
  heartbeatTimeout?: number;
}

export interface WebSocketInstance {
  connect: () => void;
  disconnect: () => void;
  send: (data: any) => void;
  isConnected: () => boolean;
}

/**
 * 创建WebSocket连接工具函数
 * 自动添加认证token和租户ID
 * 支持心跳保活和自动重连
 */
export function createWebSocket(options: WebSocketOptions): WebSocketInstance {
  const {
    uri,
    onMessage,
    onOpen,
    onClose,
    onError,
    autoReconnect = true,
    maxReconnect = 6,
    heartbeatInterval = 30000, // 30秒
    heartbeatTimeout = 10000,  // 10秒
  } = options;

  let webSocket: WebSocket | null = null;
  let lockReconnect = false;
  let reconnectTime = 0;
  let pingTimeoutObj: number | null = null;
  let pongTimeoutObj: number | null = null;
  let isManualClose = false;

  // 获取WebSocket URL
  const getWebSocketUrl = (): string => {
    const host = window.location.host;
    const baseURL = import.meta.env.VITE_API_URL;
    const token = Session.getToken();
    const tenant = Session.getTenant();

    return `ws://${host}${baseURL}${other.adaptationUrl(uri)}?access_token=${token}&TENANT-ID=${tenant}`;
  };

  // 清空定时器
  const clearTimeoutObj = () => {
    if (pingTimeoutObj) {
      clearTimeout(pingTimeoutObj);
      pingTimeoutObj = null;
    }
    if (pongTimeoutObj) {
      clearTimeout(pongTimeoutObj);
      pongTimeoutObj = null;
    }
  };

  // 开启心跳
  const startHeartbeat = () => {
    clearTimeoutObj();
    
    pingTimeoutObj = setTimeout(() => {
      if (webSocket && webSocket.readyState === WebSocket.OPEN) {
        // 发送心跳
        webSocket.send(JSON.stringify({ type: 'ping' }));
        
        // 心跳发送后，如果服务器超时未响应则断开
        pongTimeoutObj = setTimeout(() => {
          if (webSocket) {
            webSocket.close();
          }
        }, heartbeatTimeout);
      } else {
        // 连接异常，尝试重连
        reconnect();
      }
    }, heartbeatInterval);
  };

  // 重连
  const reconnect = () => {
    if (isManualClose) return;
    
    const token = Session.getToken();
    if (!token) {
      return;
    }

    if (lockReconnect || (maxReconnect !== -1 && reconnectTime >= maxReconnect)) {
      return;
    }

    lockReconnect = true;
    setTimeout(() => {
      reconnectTime++;
      connect();
      lockReconnect = false;
    }, 5000);
  };

  // 连接WebSocket
  const connect = () => {
    try {
      const wsUrl = getWebSocketUrl();
      webSocket = new WebSocket(wsUrl);

      webSocket.onopen = () => {
        reconnectTime = 0;
        isManualClose = false;
        startHeartbeat();
        onOpen?.();
      };

      webSocket.onmessage = (event) => {
        // 收到消息，重置心跳
        startHeartbeat();
        
        const data = event.data;
        
        // 忽略心跳响应
        if (data.indexOf('pong') > -1) {
          return;
        }

        try {
          const parsedData = JSON.parse(data);
          onMessage?.(parsedData);
        } catch {
          // 如果不是JSON格式，直接传递原始数据
          onMessage?.(data);
        }
      };

      webSocket.onclose = () => {
        clearTimeoutObj();
        onClose?.();
        
        if (autoReconnect && !isManualClose) {
          reconnect();
        }
      };

      webSocket.onerror = (error) => {
        onError?.(error);

        if (autoReconnect && !isManualClose) {
          reconnect();
        }
      };

    } catch (error) {
      onError?.(error as Event);
    }
  };

  // 断开连接
  const disconnect = () => {
    isManualClose = true;
    clearTimeoutObj();
    
    if (webSocket) {
      webSocket.close();
      webSocket = null;
    }
  };

  // 发送消息
  const send = (data: any) => {
    if (webSocket && webSocket.readyState === WebSocket.OPEN) {
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      webSocket.send(message);
    } else {
    }
  };

  // 检查连接状态
  const isConnected = (): boolean => {
    return webSocket?.readyState === WebSocket.OPEN;
  };

  return {
    connect,
    disconnect,
    send,
    isConnected,
  };
}

/**
 * 创建简单的WebSocket连接（只接收消息）
 * 适用于只需要接收服务器推送消息的场景
 */
export function createSimpleWebSocket(
  uri: string,
  onMessage: (data: any) => void,
  options?: Partial<WebSocketOptions>
): WebSocketInstance {
  return createWebSocket({
    uri,
    onMessage,
    ...options,
  });
}

/**
 * 创建Chart专用的WebSocket连接
 * 连接到 /hwm/ws/info，支持发送设备code切换设备
 */
export function createChartWebSocket(onMessage: (data: any) => void): WebSocketInstance {

  return createWebSocket({
    uri: '/hwm/ws/info',
    onMessage: (data) => {
      try {
        // 如果是JSON格式，解析后传递
        const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
        onMessage(parsedData);
      } catch (e) {
        // 如果不是JSON格式，直接传递原始数据
        onMessage(data);
      }
    },
    onOpen: () => {
    },
    onClose: () => {
    },
    onError: (error) => {
    },
    autoReconnect: true,
    maxReconnect: 6,
    heartbeatInterval: 30000,
    heartbeatTimeout: 10000,
  });
}
