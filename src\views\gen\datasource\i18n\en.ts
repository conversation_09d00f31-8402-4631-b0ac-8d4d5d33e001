export default {
	datasourceconf: {
		index: '#',
		importDatasourceConfTip: ' import DatasourceConf',
		id: 'id',
		name: 'name',
		url: 'url',
		username: 'username',
		password: 'password',
		docBtn: 'doc',
		createTime: 'createTime',
		updateTime: 'updateTime',
		delFlag: 'delFlag',
		tenantId: 'tenantId',
		dsType: 'dsType',
		confType: 'confType',
		dsName: 'dsName',
		instance: 'instance',
		port: 'port',
		host: 'host',
		inputidTip: 'input id',
		inputnameTip: 'input name',
		inputurlTip: 'input url',
		inputusernameTip: 'input username',
		inputpasswordTip: 'input password',
		inputcreateTimeTip: 'input createTime',
		inputupdateTimeTip: 'input updateTime',
		inputdelFlagTip: 'input delFlag',
		inputtenantIdTip: 'input tenantId',
		inputdsTypeTip: 'input dsType',
		inputconfTypeTip: 'input confType',
		inputdsNameTip: 'input dsName',
		inputinstanceTip: 'input instance',
		inputportTip: 'input port',
		inputhostTip: 'input host',
	},
};
