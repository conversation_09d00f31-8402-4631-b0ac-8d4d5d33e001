<template>
  <header class="head w-full min-h-[50px] bg-[#002451] text-white text-sm overflow-hidden">
    <div class="flex flex-wrap items-center gap-2 p-2">
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">结构物类型:</span>
        <el-select
          :model-value="searchParams.structureType"
          placeholder="请选择"
          clearable
          style="width: 110px"
          @update:modelValue="(value) => updateSearchParam('structureType', value)"
        >
          <el-option v-for="item in structureTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">管养单位:</span>
        <el-select
          :model-value="searchParams.managementName"
          placeholder="请选择"
          clearable
          style="width: 110px"
          @update:modelValue="(value) => updateSearchParam('managementName', value)"
        >
          <el-option v-for="item in managementUnitOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">预警类型:</span>
        <el-select
          :model-value="searchParams.sourceType"
          placeholder="请选择"
          clearable
          style="width: 110px"
          @update:modelValue="(value) => updateSearchParam('sourceType', value)"
        >
          <el-option v-for="item in warningTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">预警等级:</span>
        <el-select
          :model-value="searchParams.alarmLevel"
          placeholder="请选择"
          clearable
          style="width: 110px"
          @update:modelValue="(value) => updateSearchParam('alarmLevel', value)"
        >
          <el-option v-for="item in warningLevelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">处置状态:</span>
        <el-select
          :model-value="searchParams.alarmStatus"
          placeholder="请选择"
          clearable
          style="width: 110px"
          @update:modelValue="(value) => updateSearchParam('alarmStatus', value)"
        >
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">结构物名称:</span>
        <el-input
          :model-value="searchParams.structureName"
          placeholder="请输入"
          clearable
          style="width: 110px"
          @update:modelValue="(value) => updateSearchParam('structureName', value)"
        />
      </div>
      <div class="flex items-center flex-shrink-0">
        <span class="search-label mr-1">时间范围:</span>
        <el-date-picker
          :model-value="searchParams.timeRange"
          @update:modelValue="(value) => updateSearchParam('timeRange', value)"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="bg-[#003366] border-[#005099] rounded"
          popper-class="custom-date-picker-popper"
        />
      </div>
      <button @click="onSearch" class="bg-[#0066FF] hover:bg-[#0052CC] text-white font-bold py-1 px-4 rounded flex-shrink-0">
        查询
      </button>
    </div>
  </header>
</template>

<script setup>
// @ts-nocheck
import { ref, onMounted, defineProps, defineEmits } from 'vue';
import { getStructureMaintainUnit } from '/@/api/dataview/other';

const props = defineProps({
  searchParams: {
    type: Object,
    required: true
  },
  structureTypeOptions: {
    type: Array,
    default: () => []
  },
  warningTypeOptions: {
    type: Array,
    default: () => []
  },
  warningLevelOptions: {
    type: Array,
    default: () => []
  },
  statusOptions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:searchParams', 'search']);

const managementUnitOptions = ref([]);

onMounted(async () => {
  try {
    const res = await getStructureMaintainUnit();
    if (res.data && Array.isArray(res.data)) {
      managementUnitOptions.value = res.data.map((unitName) => ({
        value: unitName,
        label: unitName,
      }));
    }
  } catch (error) {
    console.error("Failed to fetch management units:", error);
    managementUnitOptions.value = [];
  }
});

const updateSearchParam = (key, value) => {
  emit('update:searchParams', { ...props.searchParams, [key]: value });
};

const onSearch = () => {
  emit('search');
};
</script>

<style scoped>
/* Add any component-specific styles here if needed */
/* The popper-class "custom-date-picker-popper" implies global styles or styles in the parent. */
/* If those styles are specific to this header, they should be moved here. */

.search-label {
  display: inline-block;
  width: 75px;
  text-align: right;
  white-space: nowrap;
}

:deep(.el-select),
:deep(.el-input) {
  .el-input__wrapper {
    background-color: #003366 !important;
    border: 1px solid #005099 !important;
    box-shadow: none !important;
    .el-input__inner {
      color: aqua;
    }
  }
}
:deep(.el-input__inner::placeholder) {
  color: aqua !important;
  opacity: 0.6;
}

select,
input[type='text'] {
  color: aqua;
}

/* Ensure el-date-picker specific classes are styled correctly if not using Tailwind for them */
:deep(.el-date-editor.el-input__wrapper) {
  background-color: #003366 !important;
  border: 1px solid #005099 !important;
  box-shadow: none !important;
  padding-top: 0px;
  padding-bottom: 0px;
}
:deep(.el-range-input) {
  background-color: transparent !important;
  color: aqua !important;
}

/* Styling for placeholder text in el-date-picker inputs */
:deep(.el-date-editor .el-input__inner::placeholder),
:deep(.el-date-editor .el-range-input::placeholder) {
  color: aqua !important;
}
/* Firefox placeholder styling */
:deep(.el-date-editor .el-input__inner::-moz-placeholder),
:deep(.el-date-editor .el-range-input::-moz-placeholder) {
  color: aqua !important;
  opacity: 1;
}
/* IE/Edge placeholder styling */
:deep(.el-date-editor .el-input__inner:-ms-input-placeholder),
:deep(.el-date-editor .el-range-input:-ms-input-placeholder) {
  color: aqua !important;
}

:deep(.el-range-separator) {
  color: aqua !important;
}
</style> 