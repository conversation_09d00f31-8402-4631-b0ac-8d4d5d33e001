<script setup>
// @ts-nocheck
import Card from './Card.vue'
import {getDeviceListStatus} from '/@/api/dataview/base'
import {useRoadFilterApi } from '/@/composables/useApi';
import { computed } from 'vue';

const { data: apiData } =useRoadFilterApi(getDeviceListStatus);

// 处理apiData数据并分组
function processData() {
  if (!apiData.value) return [];

  // 定义不同结构物的监测类型映射
  const structureTypeMap = {
    '桥梁': ['桥梁标准化监测', '轻量化监测'],
    '隧道': ['隧道结构监测', '隧道机电监测'],
    '边坡': ['边坡专业监测', '边坡事件监测'],
    '下穿通道': ['下穿通道监测']
  };

  const monitorLabelMap = {
    '桥梁标准化监测': '标准化监测',
    '轻量化监测': '轻量化监测',
    '隧道结构监测': '结构监测',
    '隧道机电监测': '机电监测',
    '边坡专业监测': '专业监测',
    '边坡事件监测': '事件监测',
    '下穿通道监测': '事件监测',
  };

  const typeToStructure = {};
  for (const [structure, types] of Object.entries(structureTypeMap)) {
    types.forEach(type => {
      typeToStructure[type] = structure;
    });
  }

  const resultMap = new Map(
    Object.keys(structureTypeMap).map(label => [
      label,
      {
        label,
        monitorType: [],
        totalOnline: 0,
        totalDevices: 0,
        onlineRate: 0,
      },
    ])
  );

  apiData.value.forEach((item) => {
    const structureName = typeToStructure[item.type];
    const structureObj = resultMap.get(structureName);

    if (structureObj) {
      structureObj.monitorType.push({
        label: monitorLabelMap[item.type] || item.type,
        offline: Number(item.offlineCount),
        total: Number(item.totalDeviceCount),
      });

      structureObj.totalOnline += Number(item.onlineCount);
      structureObj.totalDevices += Number(item.totalDeviceCount);
    }
  });

  const result = Array.from(resultMap.values());

  // 计算在线率
  result.forEach(item => {
    item.onlineRate = item.totalDevices > 0 
      ? parseFloat(((item.totalOnline / item.totalDevices) * 100).toFixed(1))
      : 0;
  });

  return result;
}

const resData = computed(() => processData());
</script>

<template>
  <div class="w-full h-full p-1">
    <div v-if="resData.length" class="parent w-full h-60">
      <div class="div1">
        <Card :data="resData[0]" />
      </div>
      <div class="div2">
        <Card :data="resData[1]" />
      </div>
      <div class="div3">
        <Card :data="resData[2]" />
      </div>
      <div class="div4">
        <Card :data="resData[3]" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashed-border::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  border-right: 1px dashed #2563eb;
}

.parent {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(3, 1fr);
  /* grid-column-gap: 0px; */
  /* grid-row-gap: 2px; */
}

.div1 {
  grid-area: 1 / 1 / 2 / 6;
}

.div2 {
  grid-area: 2 / 1 / 3 / 4;
}

.div3 {
  grid-area: 3 / 1 / 4 / 4;
}

.div4 {
  grid-area: 2 / 4 / 4 / 6;
}
</style>