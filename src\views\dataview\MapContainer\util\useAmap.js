// @ts-nocheck
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { dataview } from '/@/stores/dataview';
import AMapLoader from '@amap/amap-jsapi-loader';
import { _AMapSecurityConfig, AmapLoaderConf, mapConf } from '/@/views/dataview/MapContainer/config/MapConf.js';
import { createStructureInfoWindow } from './infoWindow.js';

const structureTypeMap = {
  1: 'bridge',
  2: 'tunnel',
  3: 'slope',
  4: 'underpass',
};

const getHighestWarningLevel = (status) => {
  if (!status) return 0;
  if (status['3']) return 3;
  if (status['2']) return 2;
  if (status['1']) return 1;
  return 0;
};

const createMarkerPoints = (structures) => {
  return structures
    .map((structure) => {
      if (!structure.longitude || !structure.latitude) {
        return null;
      }

      const warningLevel = getHighestWarningLevel(structure.warningStatus);
      const isUndisposed = structure.warningDispose && structure.warningDispose['0'];
      const structureTypeName = structureTypeMap[structure.structureType];

      if (!structureTypeName) {
        console.warn('Unknown structure type:', structure.structureType);
        return null;
      }

      let iconName = structureTypeName;
      if (warningLevel > 0 && isUndisposed) {
        iconName += warningLevel;
      }

      const iconUrl = `/image/icons/${iconName}.svg`;

      return {
        lnglat: [structure.longitude, structure.latitude],
        extData: {
          iconUrl: iconUrl,
          structureUniqueCode: structure.structureUniqueCode,
        },
      };
    })
    .filter(Boolean);
};


export function useAmap(containerId = 'container') {
  const map = ref(null);
  const AMapInstance = ref(null);
  let markerClusterer = null;
  let district = null;
  let currentInfoWindow = null;
  const store = dataview()
  const { structureCode, curPage, currentStructureInfo, structureInfoLoading, selectedStructureId, structureList,displayStructureList } = storeToRefs(store)

  let lastOpenAfterPan = null;
  let currentSelectionMenu = null;

  // 查找在指定位置重叠的结构
  const findOverlappingStructures = (position) => {
    const tolerance = 0.00001; // 坐标容差，约1米
    const [lng, lat] = [position.lng, position.lat];

    return displayStructureList.value.filter(structure => {
      if (!structure.longitude || !structure.latitude) return false;

      const distance = Math.sqrt(
        Math.pow(structure.longitude - lng, 2) +
        Math.pow(structure.latitude - lat, 2)
      );

      return distance <= tolerance;
    }).map(structure => ({
      structureUniqueCode: structure.structureUniqueCode,
      structureName: structure.structureName,
      structureType: structure.structureType,
      warningLevel: getHighestWarningLevel(structure.warningStatus)
    }));
  };

  // 显示结构选择菜单
  const showStructureSelectionMenu = (structures, position) => {
    // 关闭之前的菜单
    if (currentSelectionMenu) {
      currentSelectionMenu.close();
    }

    // 创建菜单内容
    const menuItems = structures.map(structure => {
      const warningColor = structure.warningLevel > 0 ? '#fbbf24' : '#22d3ee';
      return `
        <div class="structure-menu-item"
             data-structure-code="${structure.structureUniqueCode}"
             style="
               cursor: pointer;
               padding: 8px 12px;
               border-bottom: 1px solid rgba(34, 211, 238, 0.2);
               transition: background-color 0.2s;
             "
             onmouseover="this.style.backgroundColor='rgba(34, 211, 238, 0.1)'"
             onmouseout="this.style.backgroundColor='transparent'">
          <div style="font-size: 14px; font-weight: 500; color: ${warningColor}; margin-bottom: 2px;">
            ${structure.structureName}
          </div>
          <div style="font-size: 12px; color: rgba(34, 211, 238, 0.7);">
            代码: ${structure.structureUniqueCode}
          </div>
        </div>
      `;
    }).join('');

    const menuContent = `
      <div style="
        background: linear-gradient(135deg, #0a1446 0%, #0f1a56 100%);
        color: white;
        border-radius: 8px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(34, 211, 238, 0.3);
        min-width: 250px;
        max-width: 300px;
        font-family: system-ui, -apple-system, sans-serif;
      ">
        <div style="
          padding: 12px;
          border-bottom: 1px solid rgba(34, 211, 238, 0.2);
        ">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <h4 style="color: #22d3ee; font-weight: 500; margin: 0;">选择结构物</h4>
            <button id="selection-menu-close" style="
              color: #22d3ee;
              background: none;
              border: none;
              font-size: 18px;
              cursor: pointer;
              padding: 0;
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
            ">&times;</button>
          </div>
          <div style="font-size: 12px; color: rgba(34, 211, 238, 0.7); margin-top: 4px;">
            此位置有 ${structures.length} 个结构物
          </div>
        </div>
        <div style="max-height: 240px; overflow-y: auto;">
          ${menuItems}
        </div>
      </div>
    `;

    currentSelectionMenu = new AMapInstance.value.InfoWindow({
      isCustom: true,
      content: menuContent,
      offset: new AMapInstance.value.Pixel(0, -40),
      closeWhenClick: false
    });

    currentSelectionMenu.on('open', () => {
      // 绑定关闭按钮事件
      const closeBtn = document.getElementById('selection-menu-close');
      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          currentSelectionMenu.close();
        });
      }

      // 绑定结构物选择事件
      const menuItems = document.querySelectorAll('.structure-menu-item');
      menuItems.forEach(item => {
        item.addEventListener('click', () => {
          const structureCode = parseInt(item.getAttribute('data-structure-code'));
          currentSelectionMenu.close();
          // 添加小延迟确保菜单完全关闭后再打开信息窗口
          setTimeout(() => {
            openInfoWindowForStructure(structureCode, position);
          }, 100);
        });
      });
    });

    currentSelectionMenu.open(map.value, position);
  };

  watch(
    () => store.structureCode,
    (newCode) => {
      if (newCode) {
        store.fetchStructureInfo(newCode)
      } else {

        store.currentStructureInfo = null
      }
    },
    { immediate: true }
  )

  const openInfoWindowForStructure = (code, position) => {
    // If a window is open for the same marker, close it and do nothing.
    if (currentInfoWindow && currentInfoWindow.getIsOpen() && code === structureCode.value) {
      currentInfoWindow.close();
      return;
    }

    // If any window is open, close it first.
    if (currentInfoWindow) {
      currentInfoWindow.close();
    }

    // Function to actually create and open the window.
    const createAndOpenWindow = () => {
      if (currentStructureInfo.value) { // Ensure info is loaded
        currentInfoWindow = createStructureInfoWindow(AMapInstance.value, map.value, position, currentStructureInfo, curPage);
      }
    };

    // Check if data for this structure is already loaded.
    if (code === structureCode.value && currentStructureInfo.value) {
      createAndOpenWindow();
    } else {
      // Fetch data, then open window.
      structureCode.value = code; // This will trigger the fetch
      const unwatch = watch(structureInfoLoading, (loading) => {
        // Wait for loading to finish and ensure the loaded data corresponds to the clicked marker
        if (!loading && currentStructureInfo.value && currentStructureInfo.value.structureUniqueCode === code) {
          createAndOpenWindow();
          unwatch(); // Important to prevent multiple triggers
        }
      });
    }
  };

  watch(selectedStructureId, (newId) => {
    if (newId === null) return;

    // Step 1: Close any open window immediately, as requested by the user.
    if (currentInfoWindow && currentInfoWindow.getIsOpen()) {
      currentInfoWindow.close();
    }

    const targetStructure = structureList.value.find(s => s.structureUniqueCode === newId);

    if (targetStructure && targetStructure.longitude && targetStructure.latitude) {
      const position = [targetStructure.longitude, targetStructure.latitude];

      // Define a one-off handler for the moveend event.
      const openAfterPan = () => {
        openInfoWindowForStructure(targetStructure.structureUniqueCode, position);
        // Clean up this specific listener to avoid it firing on subsequent manual pans.
        map.value.off('moveend', openAfterPan);
      };

      // Listen for the pan to finish.
      map.value.on('moveend', openAfterPan);

      // Pan map to the location, which will trigger the 'moveend' event upon completion.
      map.value.setCenter(position);
      map.value.setZoom(15);
    }

    // Reset the ID in the store so the watcher can be triggered again for the same ID
    setTimeout(() => {
      store.selectedStructureId = null;
    }, 100);
  });

  const initMap = () => {
    window._AMapSecurityConfig = _AMapSecurityConfig;

    return AMapLoader.load(AmapLoaderConf)
      .then((AMap) => {
        AMapInstance.value = AMap;
        map.value = new AMap.Map(containerId, {
          ...mapConf,
          layers: [AMap.createDefaultLayer()],
        });

        drawBounds(AMap);
        initMarkerClusterer(AMap);

        // 监听缩放事件，更新标记显示
        map.value.on('zoomend', () => {
          // 重新设置标记以更新数字标识的显示
          if (markerClusterer && structureList.value.length > 0) {
            const points = createMarkerPoints(displayStructureList.value);
            markerClusterer.setData(points);
          }
        });

        return { map, AMapInstance };
      })
      .catch((e) => {
        console.error("AMapLoader Error:", e);
        throw e;
      });
  };

  const drawBounds = (currentAMap) => {
    if (!district) {
      const opts = { subdistrict: 0, extensions: 'all', level: 'district' };
      district = new currentAMap.DistrictSearch(opts);
    }
    district.setLevel('district');
    district.search('宁波市', (status, result) => {
      if (status === 'complete' && result.districtList.length) {
        const bounds = result.districtList[0].boundaries;
        if (bounds) {
          bounds.forEach((bound) => {
            new currentAMap.Polygon({
              map: map.value,
              strokeWeight: 2,
              path: bound,
              fillOpacity: 0.1,
              fillColor: '#064A9C',
              strokeColor: '#00D0FF',
            });
          });
        }
      }
    });
  };

  const initMarkerClusterer = (AMap) => {
    const renderClusterMarker = (context) => {
      const count = context.count;
      const div = document.createElement('div');
      div.style.position = 'relative';
      div.innerHTML = `
        <img src="/image/icons/cluster.svg" style="width:36px;height:40px;"/>
        <span style="
          position:absolute;
          right:0;
          bottom:0;
          background:#ff4d4f;
          color:#fff;
          border-radius:50%;
          width:18px;
          height:18px;
          line-height:18px;
          font-size:12px;
          text-align:center;
          border:2px solid #fff;
          box-shadow:0 0 2px #0003;
        ">${count}</span>
      `;
      context.marker.setContent(div);

      context.marker.on('click', (e) => {
        const zoom = getZoom(context.clusterData)
        const center = context.clusterData[0].lnglat;
        map.value.setZoomAndCenter(zoom, center);
      });
    };
    const getLngLat = (clusterData) => {
      return clusterData.reduce((acc, item) => {
        acc.push([item.lnglat.lng, item.lnglat.lat]);
        return acc;
      }, []);
    };
    const getZoom = (clusterData) => {
      const points = getLngLat(clusterData);

      let max = 0;
      for (const p of points)
        for (const q of points) max = Math.max(max, Math.hypot((p[0] - q[0]) * 85, (p[1] - q[1]) * 111));
      console.log();
      return -1.42 * Math.log(max) + 12.56;
    };
    const renderMarker = (context) => {
      // Set marker content
      const iconUrl = context.data[0].extData.iconUrl;
      const position = context.marker.getPosition();

      // 检查当前缩放级别和重叠结构
      const currentZoom = map.value.getZoom();
      const maxZoom = 18; // 高德地图最大缩放级别
      const overlappingStructures = findOverlappingStructures(position);

      // 如果在最大缩放级别下有多个重叠结构，显示数字标识
      let markerContent = `<img src="${iconUrl}" style="width:36px;height:40px;" />`;
      if (currentZoom >= maxZoom && overlappingStructures.length > 1) {
        markerContent = `
          <div style="position: relative;">
            <img src="${iconUrl}" style="width:36px;height:40px;" />
            <div style="
              position: absolute;
              top: -2px;
              right: -2px;
              background: #ff4d4f;
              color: white;
              border-radius: 50%;
              width: 16px;
              height: 16px;
              font-size: 10px;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 2px solid white;
              box-shadow: 0 0 3px rgba(0,0,0,0.3);
              font-weight: bold;
            ">${overlappingStructures.length}</div>
          </div>
        `;
      }

      context.marker.setContent(markerContent);
      context.marker.setOffset(new AMap.Pixel(-18, -40));

      // Pass data to marker
      context.marker.setExtData(context.data[0].extData);

      // 创建hover提示框
      let hoverTooltip = null;

      // Bind hover events for tooltip
      context.marker.on('mouseover', (e) => {
        const ext = e.target.getExtData();
        if (!ext || !ext.structureUniqueCode) return;

        // 检查是否有重叠的结构物
        const position = e.target.getPosition();
        const overlappingStructures = findOverlappingStructures(position);

        let tooltipContent = '';

        if (overlappingStructures.length > 1) {
          // 多个结构物的情况，显示堆叠样式
          const structureItems = overlappingStructures.map((structure, index) => {
            const warningColor = structure.warningLevel > 0 ? '#fbbf24' : '#22d3ee';
            return `
              <div style="
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 4px 0;
                ${index < overlappingStructures.length - 1 ? 'border-bottom: 1px solid rgba(34, 211, 238, 0.2);' : ''}
              ">
                <div style="
                  width: 6px;
                  height: 6px;
                  background: ${warningColor};
                  border-radius: 50%;
                  box-shadow: 0 0 8px rgba(34, 211, 238, 0.6);
                  animation: pulse 2s infinite;
                  animation-delay: ${index * 0.2}s;
                "></div>
                <span style="
                  text-shadow: 0 0 10px rgba(34, 211, 238, 0.3);
                  color: ${warningColor};
                  font-weight: 500;
                ">
                  ${structure.structureName}
                </span>
              </div>
            `;
          }).join('');

          tooltipContent = `
            <div style="
              background: linear-gradient(135deg, #0a1446 0%, #1a2b5c 50%, #0f1a56 100%);
              color: #22d3ee;
              padding: 10px 12px;
              border-radius: 8px;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              border: 1px solid rgba(34, 211, 238, 0.4);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1);
              backdrop-filter: blur(10px);
              position: relative;
              animation: fadeInScale 0.2s ease-out;
              min-width: 180px;
            ">
              <div style="
                font-size: 11px;
                color: rgba(34, 211, 238, 0.7);
                margin-bottom: 6px;
                text-align: center;
              ">
                此位置有 ${overlappingStructures.length} 个结构物
              </div>
              ${structureItems}
            </div>
          `;
        } else {
          // 单个结构物的情况
          const structure = structureList.value.find(s => s.structureUniqueCode === ext.structureUniqueCode);
          if (!structure) return;

          tooltipContent = `
            <div style="
              background: linear-gradient(135deg, #0a1446 0%, #1a2b5c 50%, #0f1a56 100%);
              color: #22d3ee;
              padding: 8px 12px;
              border-radius: 6px;
              font-family: 'Consolas', 'Monaco', monospace;
              font-size: 13px;
              font-weight: 500;
              border: 1px solid rgba(34, 211, 238, 0.4);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(34, 211, 238, 0.1);
              backdrop-filter: blur(10px);
              white-space: nowrap;
              position: relative;
              animation: fadeInScale 0.2s ease-out;
            ">
              <div style="
                display: flex;
                align-items: center;
                gap: 6px;
              ">
                <div style="
                  width: 6px;
                  height: 6px;
                  background: #22d3ee;
                  border-radius: 50%;
                  box-shadow: 0 0 8px rgba(34, 211, 238, 0.6);
                  animation: pulse 2s infinite;
                "></div>
                <span style="text-shadow: 0 0 10px rgba(34, 211, 238, 0.3);">
                  ${structure.structureName}
                </span>
              </div>
            </div>
          `;
        }

        tooltipContent += `
          <style>
            @keyframes fadeInScale {
              from {
                opacity: 0;
                transform: scale(0.8) translateY(5px);
              }
              to {
                opacity: 1;
                transform: scale(1) translateY(0);
              }
            }
            @keyframes pulse {
              0%, 100% {
                opacity: 1;
                transform: scale(1);
              }
              50% {
                opacity: 0.6;
                transform: scale(1.2);
              }
            }
          </style>
        `;

        hoverTooltip = new AMapInstance.value.InfoWindow({
          isCustom: true,
          content: tooltipContent,
          offset: new AMapInstance.value.Pixel(0, -50),
          closeWhenClick: false,
          autoMove: false
        });

        hoverTooltip.open(map.value, e.target.getPosition());
      });

      context.marker.on('mouseout', () => {
        if (hoverTooltip) {
          hoverTooltip.close();
          hoverTooltip = null;
        }
      });

      // Bind click event
      context.marker.on('click', (e) => {
        e.stopPropagation?.();

        // 关闭hover提示框
        if (hoverTooltip) {
          hoverTooltip.close();
          hoverTooltip = null;
        }

        const ext = e.target.getExtData();
        if (!ext || !ext.structureUniqueCode) return;

        // 重新检查当前缩放级别和重叠结构（因为用户可能已经缩放）
        const currentZoom = map.value.getZoom();
        const maxZoom = 18;

        if (currentZoom >= maxZoom) {
          const overlappingStructures = findOverlappingStructures(e.target.getPosition());
          if (overlappingStructures.length > 1) {
            showStructureSelectionMenu(overlappingStructures, e.target.getPosition());
            return;
          }
        }

        // 正常情况下直接打开信息窗口
        openInfoWindowForStructure(ext.structureUniqueCode, e.target.getPosition());
      });
    };

    markerClusterer = new AMap.MarkerCluster(map.value, [], {
      gridSize: 40,
      renderClusterMarker,
      renderMarker, // Pass the render function here
    });
  };

  const setMarkers = (structures) => {
    if (!markerClusterer) return;
    const points = createMarkerPoints(structures);
    markerClusterer.setData(points);
  };

  return {
    map,
    AMapInstance,
    initMap,
    setMarkers,
  };
} 