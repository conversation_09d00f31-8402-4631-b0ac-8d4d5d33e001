<script setup>
import Rate from './Rate.vue'
import {useRoadFilterApi } from '/@/composables/useApi';
import {getWarningStats } from '/@/api/dataview/base'
import { computed } from 'vue';
const { data: apiData } =useRoadFilterApi(getWarningStats);

const Statistics = computed(() => {
  const defaultStats = [
    { label: '桥梁', level: { one: 0, two: 0, three: 0 }, rate: 0 },
    { label: '隧道', level: { one: 0, two: 0, three: 0 }, rate: 0 },
    { label: '边坡', level: { one: 0, two: 0, three: 0 }, rate: 0 },
  ];

  if (!apiData.value) {
    return defaultStats;
  }

  const data = apiData.value;
  return defaultStats.map((item) => {
    const apiItem = data[item.label];
    if (apiItem) {
      return {
        ...item,
        level: {
          one: Number(apiItem['一级预警数量']),
          two: Number(apiItem['二级预警数量']),
          three: Number(apiItem['三级预警数量']),
        },
        rate: apiItem['处置率']*100,
      };
    }
    return item;
  });
});

const levels = {one:'一级', two:'二级', three:'三级'}
const levelColor = {one:'#2DCBFE', two:'#E1C33A', three:'#D22340'}
</script>

<template>
  <div class="gap-2 flex flex-col p-2">
    <div v-for="i in Statistics" class="border border-blue-600 w-full h-20 flex">
      <span class="absolute left-1 mt-4 z border border-x-0 text-wrap w-4 border-[#2198D1] bg-[#114976] text-[#23A1D2]">{{ i.label }}</span>
      <div v-for="(value,key,index) in i.level" 
           class="w-1/4 flex flex-col justify-center items-center relative"
           :class="{'dashed-border': index < 2}">
        <span>{{levels[key]}}</span>
        <span class="text-xl text-[#24A4D5]" :style="{color:levelColor[key]}">{{ value }}</span>
      </div>
      <div class="w-1/4 p-2"><Rate :rate="i.rate" name="处置率" /></div>
    </div>
  </div>
</template>

<style scoped>
.dashed-border::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  border-right: 1px dashed #2563eb;
}
</style> 