export default {
    syslog: {
        index: '#',
        logType: 'logType',
        title: 'title',
        remoteAddr: 'remoteAddr',
        method: 'method',
        ua: 'browser',
        serviceId: 'serviceId',
        time: 'time',
        params: 'params',
        createTime: 'createTime',
        requestUri: 'requestUri',
        exception: 'exception',
        createBy: 'createBy',
        action: 'action',
        inputLogTypeTip: 'select logType',
        inputStartPlaceholderTip: 'Start Time',
        inputEndPlaceholderTip: 'End TIme',
        result: 'result'
    },
};
